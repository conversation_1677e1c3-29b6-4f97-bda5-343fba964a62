#!/bin/bash

# 测试增量字节码加载的脚本

echo "=== Testing Incremental Bytecode Loading ==="

# 设置环境变量
export DART_VM_OPTIONS="--enable-asserts"

# 编译到AOT
echo "Step 1: Compiling to AOT..."
./tools/build.py --mode release --arch x64 runtime
./out/ReleaseX64/dart_precompiled_runtime_product --help > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: AOT runtime not found or not working"
    exit 1
fi

# 编译测试文件到AOT
echo "Step 2: Compiling test file to AOT..."
./out/ReleaseX64/dart compile aot-snapshot test_incremental_bytecode_loading.dart
if [ $? -ne 0 ]; then
    echo "Error: Failed to compile test file to AOT"
    exit 1
fi

# 运行AOT版本
echo "Step 3: Running AOT version..."
./out/ReleaseX64/dart_precompiled_runtime_product test_incremental_bytecode_loading.aot
if [ $? -ne 0 ]; then
    echo "Error: Failed to run AOT version"
    exit 1
fi

# 编译到字节码
echo "Step 4: Compiling to bytecode..."
./out/ReleaseX64/dart compile kernel test_incremental_bytecode_loading.dart
if [ $? -ne 0 ]; then
    echo "Error: Failed to compile to bytecode"
    exit 1
fi

# 测试增量加载（这里需要你的具体增量加载逻辑）
echo "Step 5: Testing incremental loading..."
echo "Note: This step requires your specific incremental loading implementation"

echo "=== Test completed ==="
echo "If you see this message, the basic compilation steps worked."
echo "The incremental loading functionality needs to be tested with your specific use case."
