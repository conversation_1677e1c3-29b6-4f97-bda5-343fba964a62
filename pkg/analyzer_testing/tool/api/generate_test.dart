// Copyright (c) 2025, the Dart project authors. Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

// This test verifies that all files generated by `generate.dart` are up to
// date.

import 'package:analyzer_utilities/tool/api.dart';
import 'package:analyzer_utilities/tools.dart';
import 'package:path/path.dart';

import 'generate.dart';

Future<void> main() async {
  await GeneratedContent.checkAll(
    analyzerTestingPkgPath,
    join(analyzerTestingPkgPath, 'tool', 'api', 'generate.dart'),
    allTargets,
  );
}
