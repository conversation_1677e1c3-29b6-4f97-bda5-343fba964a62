package:analyzer_testing/analysis_rule/analysis_rule.dart:
  error (function: ExpectedDiagnostic Function(DiagnosticCode, int, int, {Pattern? messageContains}))
  AnalysisRuleTest (class extends PubPackageResolutionTest):
    new (constructor: AnalysisRuleTest Function())
    analysisRule (getter: String)
    assertNoPubspecDiagnostics (method: Future<void> Function(String))
    assertPubspecDiagnostics (method: Future<void> Function(String, List<ExpectedDiagnostic>))
    lint (method: ExpectedDiagnostic Function(int, int, {Pattern? correctionContains, Pattern? messageContains, String? name}))
    setUp (method: void Function())
package:analyzer_testing/experiments/experiments.dart:
  experimentsForTests (static getter: List<String>)
  experimentsForTests= (static setter: List<String>)
package:analyzer_testing/mock_packages/mock_packages.dart:
  BlazeMockPackages (class extends Object):
    instance (static getter: BlazeMockPackages)
    addFlutter (method: void Function(ResourceProvider))
    addMeta (method: void Function(ResourceProvider))
  MockPackagesMixin (mixin on Object):
    packagesRootPath (getter: String)
    pathContext (getter: Context)
    resourceProvider (getter: ResourceProvider)
    addAngularMeta (method: Folder Function())
    addFfi (method: Folder Function())
    addFixnum (method: Folder Function())
    addFlutter (method: Folder Function())
    addFlutterTest (method: Folder Function())
    addJs (method: Folder Function())
    addKernel (method: Folder Function())
    addMeta (method: Folder Function())
    addPedantic (method: Folder Function())
    addTestReflectiveLoader (method: Folder Function())
    addUI (method: Folder Function())
    addVectorMath (method: Folder Function())
package:analyzer_testing/package_root.dart:
  packageRoot (static getter: String)
package:analyzer_testing/resource_provider_mixin.dart:
  ResourceProviderMixin (mixin on Object):
    pathContext (getter: Context)
    resourceProvider (getter: ResourceProvider)
    convertPath (method: String Function(String))
    deleteFile (method: void Function(String))
    deleteFolder (method: void Function(String))
    fromUri (method: String Function(Uri))
    getFile (method: File Function(String))
    getFolder (method: Folder Function(String))
    join (method: String Function(String, [String?, String?, String?, String?, String?, String?, String?]))
    modifyFile2 (method: void Function(File, String))
    newAnalysisOptionsYamlFile (method: File Function(String, String))
    newBazelBuildFile (method: File Function(String, String))
    newBuildGnFile (method: File Function(String, String))
    newFile (method: File Function(String, String))
    newFolder (method: Folder Function(String))
    newLink (method: Link Function(String, String))
    newPackageConfigJsonFile (method: File Function(String, String))
    newPackageConfigJsonFileFromBuilder (method: File Function(String, PackageConfigFileBuilder))
    newPubspecYamlFile (method: File Function(String, String))
    newSinglePackageConfigJsonFile (method: void Function({required String name, required String packagePath}))
    toUri (method: Uri Function(String))
    toUriStr (method: String Function(String))
package:analyzer_testing/src/analysis_rule/pub_package_resolution.dart:
  ExpectedDiagnostic (non-public)
  PubPackageResolutionTest (non-public)
package:analyzer_testing/utilities/extensions/resource_provider.dart:
  ResourceProviderExtension (extension on ResourceProvider):
    convertPath (method: String Function(String))
package:analyzer_testing/utilities/utilities.dart:
  analysisOptionsContent (function: String Function({List<String> experiments, List<String> includes, List<String> legacyPlugins, List<String> rules, bool strictCasts, bool strictInference, bool strictRawTypes, List<String> unignorableNames}))
  pubspecYamlContent (function: String Function({List<String> dependencies, String? name, String? sdkVersion}))
dart:async:
  Future (referenced)
dart:core:
  List (referenced)
  Object (referenced)
  Pattern (referenced)
  String (referenced)
  Uri (referenced)
  bool (referenced)
  int (referenced)
package:_fe_analyzer_shared/src/base/errors.dart:
  DiagnosticCode (referenced)
package:analyzer/file_system/file_system.dart:
  File (referenced)
  Folder (referenced)
  Link (referenced)
  ResourceProvider (referenced)
package:analyzer/utilities/package_config_file_builder.dart:
  PackageConfigFileBuilder (referenced)
package:path/src/context.dart:
  Context (referenced)
