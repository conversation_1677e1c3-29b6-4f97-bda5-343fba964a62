# Copyright (c) 2024, the Dart project authors.  Please see the AUTHORS file
# for details. All rights reserved. Use of this source code is governed by a
# BSD-style license that can be found in the LICENSE file.
callable:
  - library: 'dart:core'
  - library: 'package:expect/expect.dart'
  # Sufficient to export interface, even if implementation is private.
  - library: 'shared/shared.dart'
    class: 'Interface'
  - library: 'shared/shared.dart'
    class: 'Triple'
  # Sufficient to expose reexported name
  - library: 'shared/shared.dart'
    class: 'Exported'
