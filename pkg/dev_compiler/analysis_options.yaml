include: package:lints/recommended.yaml

analyzer:
  language:
    strict-casts: true
  errors:
    todo: ignore
    avoid_function_literals_in_foreach_calls: ignore
    avoid_renaming_method_parameters: ignore
    constant_identifier_names: ignore
    implementation_imports: ignore
    prefer_void_to_null: ignore
    # ~5 violations in test/sourcemap.
    strict_top_level_inference: ignore
  exclude:
    - doc/api/**
    - gen/**
    - node_modules/**
    - test/codegen/**
    - test/samples/**
    - test/transformer/hello_app/**

linter:
  rules:
    # Not enforced by lints at any version.
    - always_declare_return_types
    - directives_ordering
    - omit_local_variable_types
    - prefer_single_quotes
    - prefer_relative_imports
    - unawaited_futures
    - unnecessary_breaks
