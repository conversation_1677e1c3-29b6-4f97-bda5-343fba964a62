The Dart Dev Compiler (DDC) is a fast, modular compiler that generates modern
JavaScript (EcmaScript 6). Its primary use today is to support fast, iterative
development of Dart web applications for Chrome and other modern browsers.

# Support

DDC is meant to be used by build systems like bazel, `build_web_compilers` and
`flutter_tools`  under the hood. This compiler is not meant to be used by
application developers directly.

While at times the code generated by this compiler may be readable, the
representation is not meant to be stable and can break with time. For that
reason we do not recommend using this compiler to export Dart as a
JavaScript module.

The recommended approach to compile Dart to JavaScript is to use `dart compile
js` instead. If you intend to make a public JavaScript API based on a Dart
implementation, such API should be declared explicitly using the standard
Dart-JSInterop mechanisms.

# Implementation details

## Modularity

Unlike Dart2JS, DDC does not require an entire Dart application. Instead, it
operates modularly: it compiles a set of Dart files into a JavaScript module. A
DDC compilation step requires a set of input Dart files and a set of *summaries*
of dependencies. It performs modular type checking as part of this compilation
step, and, if the input type checks, it generates a JavaScript module. The
browser (i.e., the JavaScript runtime) loads and links the generated modules
when running the application. During development, a compilation step only needs
to be rerun if the Dart files or summaries it relies upon change. For most
changes, only a very small part of your code will require recompilation.
Moreover, modules that are unchanged can be cached in the browser.

## Representation

Currently Dart classes are mapped to ES6 classes, Dart fields to ES6 properties,
Dart getters/setters to ES6 getters/setters, Dart methods to ES6 methods, and so
on. Often names are preserved and calling conventions are natural JavaScript
ones.

Some Dart concepts don't map directly:

- *Libraries*. Multiple Dart libraries are mapped to a single JS module. Each
  library appears as a first class object in the generated JS module, with its
  top-level symbols as members. We currently use a heuristic (based upon file
  paths) to ensure unique naming of generated library objects.

- *Generics*. Dart generics are *reified*, i.e., they are preserved at runtime.
  Generic classes are mapped to factories that, given one or more type
  parameters, return an actual ES6 class (e.g., `HashMap$(core.String,
  core.int)` produces a class that represents a HashMap from strings to ints).
  Similarly, generic methods are mapped to factories that, given one or more
  type parameters, return a method.

- *Dynamic*. DDC supports dynamically typed code (i.e., Dart's `dynamic` type),
  but it will typically generate less readable and less efficient ES6 output as
  many type checks must be deferred to runtime. All dynamic operations are
  invoked via runtime helper code.

- *Constructors*. Dart supports multiple, named and factory constructors for a
  given class with a different initialization order for fields. Today, these
  are mapped to instance or static methods on the generated ES6 class.

- *Private members*. Dart maps private members (e.g., private fields or
  methods) to ES6 symbols. For example, `a._x` may map to `a[_x]` where `_x` is
  a symbol only defined in the scope of the generated library.

- *Scoping*. Dart scoping rules and reserved words are slightly different than
  JavaScript. While we try to preserve names wherever possible, in certain
  cases, we are required to rename.

In general, the current conventions (i.e., the Application Binary Interface or
ABI in compiler terminology) should not be considered stable. We reserve the
right to change these in the future.

# Browser support

DDC currently supports Chrome stable (though users have had success running on
FireFox and Safari).
