analyzer:
  language:
    enableSuperMixins: true
  strong-mode: true
  errors:
    # treat missing required parameters as a warning (not a hint)
    missing_required_param: warning
    # allow overriding fields (if they use super, ideally...)
    strong_mode_invalid_field_override: ignore
    # allow type narrowing
    strong_mode_down_cast_composite: ignore
    # allow having TODOs in the code
    todo: ignore
