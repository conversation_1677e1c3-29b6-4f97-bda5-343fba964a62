include: package:lints/recommended.yaml

analyzer:
  language:
    strict-casts: true
    strict-inference: true
  errors:
    # We import heavily from package:analyzer/src.
    implementation_imports: ignore
    non_constant_identifier_names: ignore
  exclude:
    - test/data/**

linter:
  rules:
    - always_declare_return_types
    - avoid_unused_constructor_parameters
    - directives_ordering
    - flutter_style_todos
    - omit_local_variable_types
    - prefer_single_quotes
    - unawaited_futures
    - unnecessary_ignore
    - unnecessary_library_directive
    - unnecessary_parenthesis
    - unreachable_from_main
