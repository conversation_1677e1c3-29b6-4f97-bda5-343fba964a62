include: package:lints/recommended.yaml

analyzer:
  language:
    strict-casts: true
  errors:
    todo: ignore
    # Remove from lints/recommended:
    constant_identifier_names: ignore
    implementation_imports: ignore
    non_constant_identifier_names: ignore
    # Existing violations (194)
    unintended_html_in_doc_comment: ignore

linter:
  rules:
    - always_declare_return_types
    - flutter_style_todos
    - omit_local_variable_types
    - prefer_single_quotes
    - unawaited_futures
    - unnecessary_async
    - unnecessary_ignore
    - unnecessary_library_directive
    - unnecessary_parenthesis
    - unreachable_from_main
