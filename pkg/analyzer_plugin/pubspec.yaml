name: analyzer_plugin
description: A framework and support code for building plugins for the analysis server.
version: 0.13.1-wip
repository: https://github.com/dart-lang/sdk/tree/main/pkg/analyzer_plugin

environment:
  sdk: ^3.5.0

resolution: workspace

dependencies:
  # See the release policy for managing this dependency at
  # pkg/analyzer/doc/implementation/releasing.md.
  analyzer: ^8.0.0-0
  collection: ^1.15.0
  dart_style: '>=2.3.7 <4.0.0'
  pub_semver: ^2.1.0
  yaml: ^3.1.0
  path: ^1.9.0

# We use 'any' version constraints here as we get our package versions from
# the dart-lang/sdk repo's DEPS file. Note that this is a special case; the
# best practice for packages is to specify their compatible version ranges.
# See also https://dart.dev/tools/pub/dependencies.
dev_dependencies:
  analyzer_testing: any
  analyzer_utilities: any
  lints: any
  linter: any
  meta: any
  test_reflective_loader: any
  test: any
