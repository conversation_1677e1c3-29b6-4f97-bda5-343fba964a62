# Building a Plugin

This is the table of contents for a set of pages that describe how to implement
a plugin. You should probably read the [Introduction][introduction],
[Package Structure][packageStructure] and [Getting Started][gettingStarted]
pages first, but the remaining pages can be read when you decide to implement
the described functionality.

## Pages

The following is a list of the pages available in this tutorial.

[Introduction][introduction] -
What is a plugin and what can it do?

[Package Structure][packageStructure] -
How the code for a plugin is structured into packages.

[Getting Started][gettingStarted] -
How to write a minimal plugin.

[Creating Edits][creatingEdits] -
How to compose the edits used in assists, fixes, and refactorings.

[Providing Quick Assists][assists] -
How to provide quick assists.

[Providing Quick Fixes][fixes] -
How to provide quick fixes associated with errors.

[Providing Code Completions][completion] -
How to provide code completion suggestions.

[Providing Navigation Information][navigation] -
How to provide navigation support.

[Providing Occurrences Information][occurrences] -
How to provide information for marking occurrences.

[Providing Outline Information][outline] -
How to provide the information to build outline structures.

[Providing Folding Information][folding] -
How to provide support for code folding.

[Debugging Plugins][debugging] -
How to debug a plugin.

[assists]: assists.md
[completion]: completion.md
[creatingEdits]: creating_edits.md
[debugging]: debugging.md
[fixes]: fixes.md
[folding]: folding.md
[gettingStarted]: getting_started.md
[introduction]: introduction.md
[navigation]: navigation.md
[occurrences]: occurrences.md
[outline]: outline.md
[packageStructure]: package_structure.md
