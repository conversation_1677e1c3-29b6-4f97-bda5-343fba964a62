// Copyright (c) 2020, the Dart project authors. Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

/// Character `A`.
const int $A = 0x41;

/// Character `a`.
const int $a = 0x61;

/// Character `d`.
const int $d = 0x64;

/// Character `i`.
const int $i = 0x69;

/// Character `s`.
const int $s = 0x73;

/// Character `Z`.
const int $Z = 0x5a;

/// Character `z`.
const int $z = 0x7a;
