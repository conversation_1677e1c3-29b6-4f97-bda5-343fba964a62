name: dtd_impl
description: A server app using the shelf package and Docker.
repository: https://github.com/dart-lang/sdk/tree/main/pkg/dtd_impl
publish_to: none

environment:
  sdk: ^3.5.0

resolution: workspace

# Use 'any' constraints here; we get our versions from the DEPS file.
dependencies:
  args: any
  collection: any
  dart_service_protocol_shared: any
  dds: any
  dtd: any
  file: any
  json_rpc_2: any
  path: any
  shelf: any
  shelf_web_socket: any
  sse: any
  stream_channel: any
  unified_analytics: any
  vm_service: any
  web_socket_channel: any

# We use 'any' version constraints here as we get our package versions from
# the dart-lang/sdk repo's DEPS file. Note that this is a special case; the
# best practice for packages is to specify their compatible version ranges.
# See also https://dart.dev/tools/pub/dependencies.
dev_dependencies:
  lints: any
  test: any
