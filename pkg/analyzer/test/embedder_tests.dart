// Copyright (c) 2016, the Dart project authors. Please see the AUTHORS file
// for details. All rights reserved. Use of this source code is governed by a
// BSD-style license that can be found in the LICENSE file.

import 'package:analyzer_testing/resource_provider_mixin.dart';

abstract class EmbedderRelatedTest with ResourceProviderMixin {
  final String emptyPath = '/home/<USER>/empty';
  final String foxPath = '/home/<USER>/fox';
  final String foxLib = '/home/<USER>/fox/lib';

  void setUp() {
    newFolder('/home/<USER>/empty');
    newFolder('/home/<USER>/fox/lib');
    newFile('/home/<USER>/fox/lib/_embedder.yaml', r'''
embedded_libs:
  "dart:deep": "deep/directory/file.dart"
  "dart:core" : "core/core.dart"
  "dart:fox": "slippy.dart"
  "dart:bear": "grizzly.dart"
  "dart:relative": "../relative.dart"
  "fart:loudly": "nomatter.dart"
''');
  }
}
