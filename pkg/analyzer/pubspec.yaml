name: analyzer
version: 8.0.0-dev
description: >-
  This package provides a library that performs static analysis of Dart code.
repository: https://github.com/dart-lang/sdk/tree/main/pkg/analyzer

environment:
  sdk: ^3.7.0

resolution: workspace

dependencies:
  # See the release policy for this dependency at
  # pkg/analyzer/doc/implementation/releasing.md.
  _fe_analyzer_shared: ^82.0.0
  collection: ^1.19.0
  convert: ^3.0.0
  crypto: ^3.0.0
  glob: ^2.0.0
  meta: ^1.15.0
  package_config: ^2.0.0
  path: ^1.9.0
  pub_semver: ^2.1.4
  source_span: ^1.8.0
  watcher: ^1.1.0
  yaml: ^3.0.0

# We use 'any' version constraints here as we get our package versions from
# the dart-lang/sdk repo's DEPS file. Note that this is a special case; the
# best practice for packages is to specify their compatible version ranges.
# See also https://dart.dev/tools/pub/dependencies.
dev_dependencies:
  analysis_server_client: any
  analyzer_testing: any
  analyzer_utilities: any
  args: any
  async: any
  checks: any
  heap_snapshot: any
  linter: any
  lints: any
  matcher: any
  test: any
  test_reflective_loader: any
  vm_service: any

