package:analyzer/analysis_rule/analysis_rule.dart:
  AbstractAnalysisRule (class extends Object, sealed (immediate subtypes: AnalysisRule, MultiAnalysisRule)):
    canUseParsedResult (getter: bool)
    description (getter: String)
    diagnosticCodes (getter: List<DiagnosticCode>)
    incompatibleRules (getter: List<String>)
    name (getter: String)
    pubspecVisitor (getter: PubspecVisitor<dynamic>?)
    reporter= (setter: DiagnosticReporter)
    state (getter: RuleState)
    registerNodeProcessors (method: void Function(RuleVisitorRegistry, RuleContext))
  AnalysisRule (class extends AbstractAnalysisRule):
    new (constructor: AnalysisRule Function({required String description, required String name, RuleState state}))
    diagnosticCode (getter: DiagnosticCode)
    diagnosticCodes (getter: List<DiagnosticCode>)
    reportAtNode (method: void Function(AstNode?, {List<Object> arguments, List<DiagnosticMessage>? contextMessages}))
    reportAtOffset (method: void Function(int, int, {List<Object> arguments, List<DiagnosticMessage>? contextMessages}))
    reportAtPubNode (method: void Function(PubspecNode, {List<Object> arguments, List<DiagnosticMessage> contextMessages}))
    reportAtToken (method: void Function(Token, {List<Object> arguments, List<DiagnosticMessage>? contextMessages}))
  MultiAnalysisRule (class extends AbstractAnalysisRule):
    new (constructor: MultiAnalysisRule Function({required String description, required String name, RuleState state}))
    reportAtNode (method: void Function(AstNode?, {List<Object> arguments, List<DiagnosticMessage>? contextMessages, required DiagnosticCode diagnosticCode}))
    reportAtOffset (method: void Function(int, int, {List<Object> arguments, List<DiagnosticMessage>? contextMessages, required DiagnosticCode diagnosticCode}))
    reportAtPubNode (method: void Function(PubspecNode, {List<Object> arguments, List<DiagnosticMessage> contextMessages, required DiagnosticCode diagnosticCode}))
    reportAtToken (method: void Function(Token, {List<Object> arguments, List<DiagnosticMessage>? contextMessages, required DiagnosticCode diagnosticCode}))
package:analyzer/analysis_rule/pubspec.dart:
  PubspecDependency (class extends Object):
    new (constructor: PubspecDependency Function())
    git (getter: PubspecGitRepo?)
    host (getter: PubspecHost?)
    name (getter: PubspecNode?)
    path (getter: PubspecEntry?)
    version (getter: PubspecEntry?)
  PubspecDependencyList (class extends Object):
    new (constructor: PubspecDependencyList Function())
  PubspecEntry (class extends Object):
    new (constructor: PubspecEntry Function(PubspecNode?, PubspecNode))
    key (getter: PubspecNode?)
    value (getter: PubspecNode)
    toString (method: String Function())
  PubspecEnvironment (class extends Object implements PubspecSection):
    new (constructor: PubspecEnvironment Function())
    flutter (getter: PubspecEntry?)
    sdk (getter: PubspecEntry?)
  PubspecGitRepo (class extends Object implements PubspecSection):
    new (constructor: PubspecGitRepo Function())
    ref (getter: PubspecEntry?)
    url (getter: PubspecEntry?)
  PubspecHost (class extends Object implements PubspecSection):
    new (constructor: PubspecHost Function())
    isShortForm (getter: bool)
    name (getter: PubspecEntry?)
    url (getter: PubspecEntry?)
  PubspecNode (class extends Object):
    new (constructor: PubspecNode Function())
    span (getter: SourceSpan)
    text (getter: String?)
  PubspecNodeList (class extends Object implements PubspecSection):
    new (constructor: PubspecNodeList Function())
    iterator (getter: Iterator<PubspecNode>)
  PubspecSection (class extends Object):
    new (constructor: PubspecSection Function())
    token (getter: PubspecNode)
  PubspecVisitor (class<T> extends Object):
    new (constructor: PubspecVisitor<T> Function())
    visitPackageAuthor (method: T? Function(PubspecEntry))
    visitPackageAuthors (method: T? Function(PubspecNodeList))
    visitPackageDependencies (method: T? Function(PubspecDependencyList))
    visitPackageDependency (method: T? Function(PubspecDependency))
    visitPackageDependencyOverride (method: T? Function(PubspecDependency))
    visitPackageDependencyOverrides (method: T? Function(PubspecDependencyList))
    visitPackageDescription (method: T? Function(PubspecEntry))
    visitPackageDevDependencies (method: T? Function(PubspecDependencyList))
    visitPackageDevDependency (method: T? Function(PubspecDependency))
    visitPackageDocumentation (method: T? Function(PubspecEntry))
    visitPackageEnvironment (method: T? Function(PubspecEnvironment))
    visitPackageHomepage (method: T? Function(PubspecEntry))
    visitPackageIssueTracker (method: T? Function(PubspecEntry))
    visitPackageName (method: T? Function(PubspecEntry))
    visitPackageRepository (method: T? Function(PubspecEntry))
    visitPackageVersion (method: T? Function(PubspecEntry))
package:analyzer/analysis_rule/rule_context.dart:
  RuleContext (class extends Object):
    new (constructor: RuleContext Function())
    allUnits (getter: List<RuleContextUnit>)
    currentUnit (getter: RuleContextUnit?)
    definingUnit (getter: RuleContextUnit)
    isInLibDir (getter: bool)
    isInTestDirectory (getter: bool)
    libraryElement (getter: LibraryElement?)
    package (getter: WorkspacePackage?)
    typeProvider (getter: TypeProvider)
    typeSystem (getter: TypeSystem)
    isFeatureEnabled (method: bool Function(Feature))
  RuleContextUnit (class extends Object):
    new (constructor: RuleContextUnit Function({required String content, required DiagnosticReporter diagnosticReporter, required File file, required CompilationUnit unit}))
    content (getter: String)
    diagnosticReporter (getter: DiagnosticReporter)
    file (getter: File)
    unit (getter: CompilationUnit)
package:analyzer/analysis_rule/rule_state.dart:
  dart2_12 (static getter: Version)
  dart3 (static getter: Version)
  dart3_3 (static getter: Version)
  RuleState (class extends Object):
    deprecated (constructor: RuleState Function({String? replacedBy, Version? since}))
    experimental (constructor: RuleState Function({Version? since}))
    internal (constructor: RuleState Function({Version? since}))
    removed (constructor: RuleState Function({String? replacedBy, Version? since}))
    stable (constructor: RuleState Function({Version? since}))
    isDeprecated (getter: bool)
    isExperimental (getter: bool)
    isInternal (getter: bool)
    isRemoved (getter: bool)
    label (getter: String)
    replacedBy (getter: String?)
    since (getter: Version?)
package:analyzer/analysis_rule/rule_visitor_registry.dart:
  RuleVisitorRegistry (class extends Object):
    new (constructor: RuleVisitorRegistry Function())
    addAdjacentStrings (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAnnotation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addArgumentList (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAsExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAssertInitializer (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAssertStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAssignedVariablePattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAssignmentExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAugmentedExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAugmentedInvocation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addAwaitExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addBinaryExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addBlock (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addBlockFunctionBody (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addBooleanLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addBreakStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCascadeExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCaseClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCastPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCatchClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCatchClauseParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addClassDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addClassTypeAlias (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addComment (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCommentReference (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addCompilationUnit (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConditionalExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConfiguration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstantPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstructorDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstructorFieldInitializer (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstructorName (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstructorReference (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addConstructorSelector (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addContinueStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDeclaredIdentifier (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDeclaredVariablePattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDefaultFormalParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDoStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDotShorthandConstructorInvocation (method: void Function(AnalysisRule, AstVisitor<dynamic>))
    addDotShorthandInvocation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDotShorthandPropertyAccess (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDottedName (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addDoubleLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addEmptyFunctionBody (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addEmptyStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addEnumConstantArguments (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addEnumConstantDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addEnumDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExportDirective (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExpressionFunctionBody (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExpressionStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExtendsClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExtensionDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExtensionOnClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExtensionOverride (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addExtensionTypeDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFieldDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFieldFormalParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForEachPartsWithDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForEachPartsWithIdentifier (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForEachPartsWithPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForElement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForPartsWithDeclarations (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForPartsWithExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForPartsWithPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addForStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFormalParameterList (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionDeclarationStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionExpressionInvocation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionReference (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionTypeAlias (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addFunctionTypedFormalParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addGenericFunctionType (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addGenericTypeAlias (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addGuardedPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addHideCombinator (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addIfElement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addIfStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addImplementsClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addImplicitCallReference (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addImportDirective (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addImportPrefixReference (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addIndexExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addInstanceCreationExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addIntegerLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addInterpolationExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addInterpolationString (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addIsExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLabel (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLabeledStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLibraryDirective (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLibraryIdentifier (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addListLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addListPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLogicalAndPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addLogicalOrPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMapLiteralEntry (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMapPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMapPatternEntry (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMethodDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMethodInvocation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMixinDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addMixinOnClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNamedExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNamedType (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNativeClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNativeFunctionBody (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNullAssertPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNullCheckPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addNullLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addObjectPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addParenthesizedExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addParenthesizedPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPartDirective (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPartOfDirective (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPatternAssignment (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPatternField (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPatternFieldName (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPatternVariableDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPatternVariableDeclarationStatement (method: void Function(AnalysisRule, AstVisitor<dynamic>))
    addPostfixExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPrefixExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPrefixedIdentifier (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addPropertyAccess (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRecordLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRecordPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRecordTypeAnnotation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRedirectingConstructorInvocation (method: void Function(AnalysisRule, AstVisitor<dynamic>))
    addRelationalPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRepresentationConstructorName (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRepresentationDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRestPatternElement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addRethrowExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addReturnStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addScriptTag (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSetOrMapLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addShowCombinator (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSimpleFormalParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSimpleIdentifier (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSimpleStringLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSpreadElement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addStringInterpolation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSuperConstructorInvocation (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSuperExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSuperFormalParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchCase (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchDefault (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchExpressionCase (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchPatternCase (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSwitchStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addSymbolLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addThisExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addThrowExpression (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTopLevelVariableDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTryStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTypeArgumentList (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTypeLiteral (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTypeParameter (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addTypeParameterList (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addVariableDeclaration (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addVariableDeclarationList (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addVariableDeclarationStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addWhenClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addWhileStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addWildcardPattern (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addWithClause (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    addYieldStatement (method: void Function(AbstractAnalysisRule, AstVisitor<dynamic>))
    afterLibrary (method: void Function(AbstractAnalysisRule, void Function()))
package:analyzer/dart/analysis/analysis_context.dart:
  AnalysisContext (class extends Object):
    new (constructor: AnalysisContext Function())
    contextRoot (getter: ContextRoot)
    currentSession (getter: AnalysisSession)
    sdkRoot (getter: Folder?)
    applyPendingFileChanges (method: Future<List<String>> Function())
    changeFile (method: void Function(String))
    getAnalysisOptionsForFile (method: AnalysisOptions Function(File), experimental)
package:analyzer/dart/analysis/analysis_context_collection.dart:
  AnalysisContextCollection (class extends Object):
    new (constructor: AnalysisContextCollection Function({List<String>? excludedPaths, required List<String> includedPaths, ResourceProvider? resourceProvider, String? sdkPath}))
    contexts (getter: List<AnalysisContext>)
    contextFor (method: AnalysisContext Function(String))
    dispose (method: Future<void> Function())
package:analyzer/dart/analysis/analysis_options.dart:
  AnalysisOptions (class extends Object):
    new (constructor: AnalysisOptions Function())
    chromeOsManifestChecks (getter: bool)
    codeStyleOptions (getter: CodeStyleOptions)
    contextFeatures (getter: FeatureSet)
    enabledLegacyPluginNames (getter: List<String>)
    errorProcessors (getter: List<ErrorProcessor>)
    excludePatterns (getter: List<String>)
    formatterOptions (getter: FormatterOptions)
    lint (getter: bool)
    lintRules (getter: List<AbstractAnalysisRule>)
    pluginConfigurations (getter: List<PluginConfiguration>)
    strictCasts (getter: bool)
    strictInference (getter: bool)
    strictRawTypes (getter: bool)
    warning (getter: bool)
    isLintEnabled (method: bool Function(String))
  GitPluginSource (class extends Object implements PluginSource):
    new (constructor: GitPluginSource Function({String? path, String? ref, required String url}))
    toYaml (method: String Function({required String name}))
  PathPluginSource (class extends Object implements PluginSource):
    new (constructor: PathPluginSource Function({required String path}))
    toYaml (method: String Function({required String name}))
  PluginConfiguration (class extends Object):
    new (constructor: PluginConfiguration Function({Map<String, RuleConfig> diagnosticConfigs, bool isEnabled, required String name, required PluginSource source}))
    diagnosticConfigs (getter: Map<String, RuleConfig>)
    isEnabled (getter: bool)
    name (getter: String)
    source (getter: PluginSource)
    sourceYaml (method: String Function())
  PluginSource (class extends Object, sealed (immediate subtypes: GitPluginSource, PathPluginSource, VersionedPluginSource)):
    toYaml (method: String Function({required String name}))
  VersionedPluginSource (class extends Object implements PluginSource):
    new (constructor: VersionedPluginSource Function({required String constraint}))
    toYaml (method: String Function({required String name}))
package:analyzer/dart/analysis/code_style_options.dart:
  CodeStyleOptions (class extends Object):
    new (constructor: CodeStyleOptions Function())
    addTrailingCommas (getter: bool)
    avoidRenamingMethodParameters (getter: bool)
    finalInForEach (getter: bool)
    makeLocalsFinal (getter: bool)
    preferConstDeclarations (getter: bool)
    preferIntLiterals (getter: bool)
    preferredQuoteForStrings (getter: String)
    sortCombinators (getter: bool)
    sortConstructorsFirst (getter: bool)
    specifyReturnTypes (getter: bool)
    specifyTypes (getter: bool)
    useFormatter (getter: bool)
    usePackageUris (getter: bool)
    useRelativeUris (getter: bool)
    preferredQuoteForUris (method: String Function(Iterable<NamespaceDirective>))
package:analyzer/dart/analysis/context_root.dart:
  ContextRoot (class extends Object):
    new (constructor: ContextRoot Function())
    excluded (getter: List<Resource>)
    excludedPaths (getter: Iterable<String>)
    included (getter: List<Resource>)
    includedPaths (getter: Iterable<String>)
    optionsFile (getter: File?)
    packagesFile (getter: File?)
    resourceProvider (getter: ResourceProvider)
    root (getter: Folder)
    workspace (getter: Workspace)
    analyzedFiles (method: Iterable<String> Function())
    isAnalyzed (method: bool Function(String))
package:analyzer/dart/analysis/declared_variables.dart:
  DeclaredVariables (class extends Object):
    fromMap (constructor: DeclaredVariables Function(Map<String, String>))
    new (constructor: DeclaredVariables Function())
    variableNames (getter: Iterable<String>)
    get (method: String? Function(String))
package:analyzer/dart/analysis/features.dart:
  Feature (class extends Object):
    augmentations (static getter: ExperimentalFeature)
    class_modifiers (static getter: ExperimentalFeature)
    constant_update_2018 (static getter: ExperimentalFeature)
    constructor_tearoffs (static getter: ExperimentalFeature)
    control_flow_collections (static getter: ExperimentalFeature)
    digit_separators (static getter: ExperimentalFeature)
    dot_shorthands (static getter: ExperimentalFeature)
    enhanced_enums (static getter: ExperimentalFeature)
    enhanced_parts (static getter: ExperimentalFeature)
    extension_methods (static getter: ExperimentalFeature)
    generic_metadata (static getter: ExperimentalFeature)
    getter_setter_error (static getter: ExperimentalFeature)
    inference_update_1 (static getter: ExperimentalFeature)
    inference_update_2 (static getter: ExperimentalFeature)
    inference_update_3 (static getter: ExperimentalFeature)
    inference_update_4 (static getter: ExperimentalFeature)
    inference_using_bounds (static getter: ExperimentalFeature)
    inline_class (static getter: ExperimentalFeature)
    macros (static getter: ExperimentalFeature)
    named_arguments_anywhere (static getter: ExperimentalFeature)
    non_nullable (static getter: ExperimentalFeature)
    nonfunction_type_aliases (static getter: ExperimentalFeature)
    null_aware_elements (static getter: ExperimentalFeature)
    patterns (static getter: ExperimentalFeature)
    records (static getter: ExperimentalFeature)
    sealed_class (static getter: ExperimentalFeature)
    set_literals (static getter: ExperimentalFeature)
    sound_flow_analysis (static getter: ExperimentalFeature)
    spread_collections (static getter: ExperimentalFeature)
    super_parameters (static getter: ExperimentalFeature)
    triple_shift (static getter: ExperimentalFeature)
    unnamedLibraries (static getter: ExperimentalFeature)
    unquotedImports (static getter: ExperimentalFeature)
    variance (static getter: ExperimentalFeature)
    wildcard_variables (static getter: ExperimentalFeature)
    new (constructor: Feature Function())
    experimentalFlag (getter: String?)
    releaseVersion (getter: Version?)
    status (getter: FeatureStatus)
  FeatureSet (class extends Object):
    fromEnableFlags2 (constructor: FeatureSet Function({required List<String> flags, required Version sdkLanguageVersion}))
    latestLanguageVersion (constructor: FeatureSet Function({List<String> flags}))
    isEnabled (method: bool Function(Feature))
    restrictToVersion (method: FeatureSet Function(Version))
  FeatureStatus (enum):
    abandoned (static getter: FeatureStatus)
    current (static getter: FeatureStatus)
    future (static getter: FeatureStatus)
    provisional (static getter: FeatureStatus)
    values (static getter: List<FeatureStatus>)
package:analyzer/dart/analysis/formatter_options.dart:
  FormatterOptions (class extends Object):
    new (constructor: FormatterOptions Function({int? pageWidth, TrailingCommas? trailingCommas}))
    pageWidth (getter: int?)
    trailingCommas (getter: TrailingCommas?)
  TrailingCommas (enum):
    automate (static getter: TrailingCommas)
    preserve (static getter: TrailingCommas)
    values (static getter: List<TrailingCommas>)
package:analyzer/dart/analysis/results.dart:
  AnalysisResult (class extends Object):
    new (constructor: AnalysisResult Function())
    session (getter: AnalysisSession)
  AnalysisResultWithDiagnostics (class extends Object implements FileResult):
    new (constructor: AnalysisResultWithDiagnostics Function())
    diagnostics (getter: List<Diagnostic>)
    errors (getter: List<Diagnostic>, deprecated)
  CannotResolveUriResult (class extends Object implements InvalidResult, SomeLibraryElementResult, SomeParsedLibraryResult, SomeResolvedLibraryResult):
    new (constructor: CannotResolveUriResult Function())
  DisposedAnalysisContextResult (class extends Object implements InvalidResult, SomeErrorsResult, SomeFileResult, SomeParsedLibraryResult, SomeParsedUnitResult, SomeResolvedLibraryResult, SomeResolvedUnitResult, SomeUnitElementResult):
    new (constructor: DisposedAnalysisContextResult Function())
  ElementDeclarationResult (class extends Object, deprecated):
    new (constructor: ElementDeclarationResult Function())
    fragment (getter: Fragment)
    node (getter: AstNode)
    parsedUnit (getter: ParsedUnitResult?)
    resolvedUnit (getter: ResolvedUnitResult?)
  ErrorsResult (class extends Object implements SomeErrorsResult, AnalysisResultWithDiagnostics):
    new (constructor: ErrorsResult Function())
  FileResult (class extends Object implements SomeFileResult, AnalysisResult):
    new (constructor: FileResult Function())
    analysisOptions (getter: AnalysisOptions)
    content (getter: String)
    file (getter: File)
    isLibrary (getter: bool)
    isPart (getter: bool)
    lineInfo (getter: LineInfo)
    path (getter: String)
    uri (getter: Uri)
  FragmentDeclarationResult (class extends Object):
    new (constructor: FragmentDeclarationResult Function())
    fragment (getter: Fragment)
    node (getter: AstNode)
    parsedUnit (getter: ParsedUnitResult?)
    resolvedUnit (getter: ResolvedUnitResult?)
  InvalidPathResult (class extends Object implements InvalidResult, SomeErrorsResult, SomeFileResult, SomeParsedLibraryResult, SomeParsedUnitResult, SomeResolvedLibraryResult, SomeResolvedUnitResult, SomeUnitElementResult):
    new (constructor: InvalidPathResult Function())
  InvalidResult (class extends Object):
    new (constructor: InvalidResult Function())
  LibraryElementResult (class extends Object implements SomeLibraryElementResult):
    new (constructor: LibraryElementResult Function())
    element2 (getter: LibraryElement, experimental)
  NotElementOfThisSessionResult (class extends Object implements InvalidResult, SomeParsedLibraryResult, SomeResolvedLibraryResult):
    new (constructor: NotElementOfThisSessionResult Function())
  NotLibraryButPartResult (class extends Object implements InvalidResult, SomeLibraryElementResult, SomeParsedLibraryResult, SomeResolvedLibraryResult):
    new (constructor: NotLibraryButPartResult Function())
  NotPathOfUriResult (class extends Object implements InvalidResult, SomeErrorsResult, SomeParsedLibraryResult, SomeResolvedLibraryResult, SomeResolvedUnitResult, SomeUnitElementResult):
    new (constructor: NotPathOfUriResult Function())
  ParseStringResult (class extends Object):
    new (constructor: ParseStringResult Function())
    content (getter: String)
    errors (getter: List<Diagnostic>)
    lineInfo (getter: LineInfo)
    unit (getter: CompilationUnit)
  ParsedLibraryResult (class extends Object implements SomeParsedLibraryResult, AnalysisResult):
    new (constructor: ParsedLibraryResult Function())
    units (getter: List<ParsedUnitResult>)
    getElementDeclaration2 (method: ElementDeclarationResult? Function(Fragment), deprecated, experimental)
    getFragmentDeclaration (method: FragmentDeclarationResult? Function(Fragment), experimental)
  ParsedUnitResult (class extends Object implements SomeParsedUnitResult, AnalysisResultWithDiagnostics):
    new (constructor: ParsedUnitResult Function())
    unit (getter: CompilationUnit)
  ResolvedLibraryResult (class extends Object implements ParsedLibraryResult, SomeResolvedLibraryResult):
    new (constructor: ResolvedLibraryResult Function())
    element2 (getter: LibraryElement, experimental)
    typeProvider (getter: TypeProvider)
    units (getter: List<ResolvedUnitResult>)
    unitWithPath (method: ResolvedUnitResult? Function(String))
  ResolvedUnitResult (class extends Object implements ParsedUnitResult, SomeResolvedUnitResult):
    new (constructor: ResolvedUnitResult Function())
    exists (getter: bool)
    libraryElement2 (getter: LibraryElement, experimental)
    libraryFragment (getter: LibraryFragment, experimental)
    typeProvider (getter: TypeProvider)
    typeSystem (getter: TypeSystem)
  SomeErrorsResult (class extends Object):
    new (constructor: SomeErrorsResult Function())
  SomeFileResult (class extends Object):
    new (constructor: SomeFileResult Function())
  SomeLibraryElementResult (class extends Object):
    new (constructor: SomeLibraryElementResult Function())
  SomeParsedLibraryResult (class extends Object):
    new (constructor: SomeParsedLibraryResult Function())
  SomeParsedUnitResult (class extends Object):
    new (constructor: SomeParsedUnitResult Function())
  SomeResolvedLibraryResult (class extends Object):
    new (constructor: SomeResolvedLibraryResult Function())
  SomeResolvedUnitResult (class extends Object):
    new (constructor: SomeResolvedUnitResult Function())
  SomeUnitElementResult (class extends Object):
    new (constructor: SomeUnitElementResult Function())
  UnitElementResult (class extends Object implements SomeUnitElementResult, FileResult):
    new (constructor: UnitElementResult Function())
    fragment (getter: LibraryFragment, experimental)
  UnspecifiedInvalidResult (class extends Object implements InvalidResult, SomeErrorsResult, SomeFileResult, SomeLibraryElementResult, SomeParsedLibraryResult, SomeParsedUnitResult, SomeResolvedLibraryResult, SomeResolvedUnitResult, SomeUnitElementResult):
    new (constructor: UnspecifiedInvalidResult Function())
  UriOfExternalLibraryResult (class extends Object implements InvalidResult, SomeParsedLibraryResult, SomeResolvedLibraryResult):
    new (constructor: UriOfExternalLibraryResult Function())
  AnalysisResultWithErrors (type alias for AnalysisResultWithDiagnostics, deprecated)
package:analyzer/dart/analysis/session.dart:
  AnalysisSession (class extends Object):
    new (constructor: AnalysisSession Function())
    analysisContext (getter: AnalysisContext)
    declaredVariables (getter: DeclaredVariables)
    resourceProvider (getter: ResourceProvider)
    uriConverter (getter: UriConverter)
    getErrors (method: Future<SomeErrorsResult> Function(String))
    getFile (method: SomeFileResult Function(String))
    getLibraryByUri (method: Future<SomeLibraryElementResult> Function(String))
    getParsedLibrary (method: SomeParsedLibraryResult Function(String))
    getParsedLibraryByElement2 (method: SomeParsedLibraryResult Function(LibraryElement), experimental)
    getParsedUnit (method: SomeParsedUnitResult Function(String))
    getResolvedLibrary (method: Future<SomeResolvedLibraryResult> Function(String))
    getResolvedLibraryByElement2 (method: Future<SomeResolvedLibraryResult> Function(LibraryElement), experimental)
    getResolvedLibraryContaining (method: Future<SomeResolvedLibraryResult> Function(String))
    getResolvedUnit (method: Future<SomeResolvedUnitResult> Function(String))
    getUnitElement (method: Future<SomeUnitElementResult> Function(String))
  InconsistentAnalysisException (class extends AnalysisException):
    new (constructor: InconsistentAnalysisException Function())
package:analyzer/dart/analysis/uri_converter.dart:
  UriConverter (class extends Object):
    new (constructor: UriConverter Function())
    pathToUri (method: Uri? Function(String, {String? containingPath}))
    uriToPath (method: String? Function(Uri))
package:analyzer/dart/analysis/utilities.dart:
  parseFile (function: ParseStringResult Function({required FeatureSet featureSet, required String path, ResourceProvider? resourceProvider, bool throwIfDiagnostics}))
  parseString (function: ParseStringResult Function({required String content, FeatureSet? featureSet, String? path, bool throwIfDiagnostics}))
  resolveFile2 (function: Future<SomeResolvedUnitResult> Function({required String path, ResourceProvider? resourceProvider}))
package:analyzer/dart/ast/ast.dart:
  AdjacentStrings (class extends Object implements StringLiteral):
    strings (getter: NodeList<StringLiteral>)
  AnnotatedNode (class extends Object implements AstNode):
    documentationComment (getter: Comment?)
    firstTokenAfterCommentAndMetadata (getter: Token)
    metadata (getter: NodeList<Annotation>)
    sortedCommentAndAnnotations (getter: List<AstNode>)
  Annotation (class extends Object implements AstNode):
    arguments (getter: ArgumentList?)
    atSign (getter: Token)
    constructorName (getter: SimpleIdentifier?)
    element2 (getter: Element?, experimental)
    elementAnnotation (getter: ElementAnnotation?)
    name (getter: Identifier)
    parent (getter: AstNode)
    period (getter: Token?)
    typeArguments (getter: TypeArgumentList?)
  ArgumentList (class extends Object implements AstNode):
    arguments (getter: NodeList<Expression>)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  AsExpression (class extends Object implements Expression):
    asOperator (getter: Token)
    expression (getter: Expression)
    type (getter: TypeAnnotation)
  AssertInitializer (class extends Object implements Assertion, ConstructorInitializer)
  AssertStatement (class extends Object implements Assertion, Statement):
    semicolon (getter: Token)
  Assertion (class extends Object implements AstNode):
    assertKeyword (getter: Token)
    comma (getter: Token?)
    condition (getter: Expression)
    leftParenthesis (getter: Token)
    message (getter: Expression?)
    rightParenthesis (getter: Token)
  AssignedVariablePattern (class extends Object implements VariablePattern):
    element2 (getter: Element?, experimental)
  AssignmentExpression (class extends Object implements NullShortableExpression, MethodReferenceExpression, CompoundAssignmentExpression):
    leftHandSide (getter: Expression)
    operator (getter: Token)
    rightHandSide (getter: Expression)
  AstNode (class extends Object implements SyntacticEntity):
    LEXICAL_ORDER (static getter: int Function(AstNode, AstNode))
    LEXICAL_ORDER= (static setter: int Function(AstNode, AstNode))
    beginToken (getter: Token)
    childEntities (getter: Iterable<SyntacticEntity>)
    end (getter: int)
    endToken (getter: Token)
    isSynthetic (getter: bool)
    length (getter: int)
    offset (getter: int)
    parent (getter: AstNode?)
    root (getter: AstNode)
    accept (method: E? Function<E>(AstVisitor<E>))
    findPrevious (method: Token? Function(Token))
    thisOrAncestorMatching (method: E? Function<E extends AstNode>(bool Function(AstNode)))
    thisOrAncestorOfType (method: E? Function<E extends AstNode>())
    toSource (method: String Function())
    toString (method: String Function())
    visitChildren (method: void Function(AstVisitor<dynamic>))
  AstVisitor (class<R> extends Object):
    new (constructor: AstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
  AugmentedExpression (class extends Object implements Expression):
    augmentedKeyword (getter: Token)
    fragment (getter: Fragment?, experimental)
  AugmentedInvocation (class extends Object implements Expression):
    arguments (getter: ArgumentList)
    augmentedKeyword (getter: Token)
    fragment (getter: ExecutableFragment?, experimental)
    typeArguments (getter: TypeArgumentList?)
  AwaitExpression (class extends Object implements Expression):
    awaitKeyword (getter: Token)
    expression (getter: Expression)
  BinaryExpression (class extends Object implements Expression, MethodReferenceExpression):
    leftOperand (getter: Expression)
    operator (getter: Token)
    rightOperand (getter: Expression)
    staticInvokeType (getter: FunctionType?)
  Block (class extends Object implements Statement):
    leftBracket (getter: Token)
    rightBracket (getter: Token)
    statements (getter: NodeList<Statement>)
  BlockFunctionBody (class extends Object implements FunctionBody):
    block (getter: Block)
  BooleanLiteral (class extends Object implements Literal):
    literal (getter: Token)
    value (getter: bool)
  BreakStatement (class extends Object implements Statement):
    breakKeyword (getter: Token)
    label (getter: SimpleIdentifier?)
    semicolon (getter: Token)
    target (getter: AstNode?)
  CascadeExpression (class extends Object implements Expression, NullShortableExpression):
    cascadeSections (getter: NodeList<Expression>)
    isNullAware (getter: bool)
    target (getter: Expression)
  CaseClause (class extends Object implements AstNode):
    caseKeyword (getter: Token)
    guardedPattern (getter: GuardedPattern)
  CastPattern (class extends Object implements DartPattern):
    asToken (getter: Token)
    pattern (getter: DartPattern)
    type (getter: TypeAnnotation)
  CatchClause (class extends Object implements AstNode):
    body (getter: Block)
    catchKeyword (getter: Token?)
    comma (getter: Token?)
    exceptionParameter (getter: CatchClauseParameter?)
    exceptionType (getter: TypeAnnotation?)
    leftParenthesis (getter: Token?)
    onKeyword (getter: Token?)
    rightParenthesis (getter: Token?)
    stackTraceParameter (getter: CatchClauseParameter?)
  CatchClauseParameter (class extends AstNode):
    declaredElement2 (getter: LocalVariableElement?, experimental)
    declaredFragment (getter: LocalVariableFragment?, experimental)
    name (getter: Token)
  ClassDeclaration (class extends Object implements NamedCompilationUnitMember):
    abstractKeyword (getter: Token?)
    augmentKeyword (getter: Token?, experimental)
    baseKeyword (getter: Token?)
    classKeyword (getter: Token)
    declaredFragment (getter: ClassFragment?, experimental)
    extendsClause (getter: ExtendsClause?)
    finalKeyword (getter: Token?)
    implementsClause (getter: ImplementsClause?)
    interfaceKeyword (getter: Token?)
    leftBracket (getter: Token)
    macroKeyword (getter: Token?, deprecated)
    members (getter: NodeList<ClassMember>)
    mixinKeyword (getter: Token?)
    nativeClause (getter: NativeClause?)
    rightBracket (getter: Token)
    sealedKeyword (getter: Token?)
    typeParameters (getter: TypeParameterList?)
    withClause (getter: WithClause?)
  ClassMember (class extends Object implements Declaration, sealed (immediate subtypes: ClassMemberImpl, ConstructorDeclaration, FieldDeclaration, MethodDeclaration))
  ClassTypeAlias (class extends Object implements TypeAlias):
    abstractKeyword (getter: Token?)
    baseKeyword (getter: Token?)
    declaredFragment (getter: ClassFragment?, experimental)
    equals (getter: Token)
    finalKeyword (getter: Token?)
    implementsClause (getter: ImplementsClause?)
    interfaceKeyword (getter: Token?)
    mixinKeyword (getter: Token?)
    sealedKeyword (getter: Token?)
    superclass (getter: NamedType)
    typeParameters (getter: TypeParameterList?)
    withClause (getter: WithClause)
  CollectionElement (class extends Object implements AstNode, sealed (immediate subtypes: CollectionElementImpl, Expression, ForElement, IfElement, MapLiteralEntry, NullAwareElement, SpreadElement))
  Combinator (class extends Object implements AstNode, sealed (immediate subtypes: CombinatorImpl, HideCombinator, ShowCombinator)):
    keyword (getter: Token)
  Comment (class extends Object implements AstNode):
    codeBlocks (getter: List<MdCodeBlock>, experimental)
    docDirectives (getter: List<DocDirective>, experimental)
    docImports (getter: List<DocImport>, experimental)
    hasNodoc (getter: bool, experimental)
    references (getter: NodeList<CommentReference>)
    tokens (getter: List<Token>)
  CommentReferableExpression (class extends Object implements Expression)
  CommentReference (class extends Object implements AstNode):
    expression (getter: CommentReferableExpression)
    newKeyword (getter: Token?)
  CompilationUnit (class extends Object implements AstNode):
    beginToken (getter: Token)
    declarations (getter: NodeList<CompilationUnitMember>)
    declaredFragment (getter: LibraryFragment?, experimental)
    directives (getter: NodeList<Directive>)
    endToken (getter: Token)
    featureSet (getter: FeatureSet)
    languageVersion (getter: LibraryLanguageVersion)
    languageVersionToken (getter: LanguageVersionToken?)
    lineInfo (getter: LineInfo)
    scriptTag (getter: ScriptTag?)
    sortedDirectivesAndDeclarations (getter: List<AstNode>)
    nodeCovering (method: AstNode? Function({int length, required int offset}))
  CompilationUnitMember (class extends Object implements Declaration)
  CompoundAssignmentExpression (class extends Object implements Expression):
    readElement2 (getter: Element?, experimental)
    readType (getter: DartType?)
    writeElement2 (getter: Element?, experimental)
    writeType (getter: DartType?)
  ConditionalExpression (class extends Object implements Expression):
    colon (getter: Token)
    condition (getter: Expression)
    elseExpression (getter: Expression)
    question (getter: Token)
    thenExpression (getter: Expression)
  Configuration (class extends Object implements AstNode):
    equalToken (getter: Token?)
    ifKeyword (getter: Token)
    leftParenthesis (getter: Token)
    name (getter: DottedName)
    resolvedUri (getter: DirectiveUri?)
    rightParenthesis (getter: Token)
    uri (getter: StringLiteral)
    value (getter: StringLiteral?)
  ConstantPattern (class extends Object implements DartPattern):
    constKeyword (getter: Token?)
    expression (getter: Expression)
  ConstructorDeclaration (class extends Object implements ClassMember):
    augmentKeyword (getter: Token?)
    body (getter: FunctionBody)
    constKeyword (getter: Token?)
    declaredFragment (getter: ConstructorFragment?, experimental)
    externalKeyword (getter: Token?)
    factoryKeyword (getter: Token?)
    initializers (getter: NodeList<ConstructorInitializer>)
    name (getter: Token?)
    parameters (getter: FormalParameterList)
    period (getter: Token?)
    redirectedConstructor (getter: ConstructorName?)
    returnType (getter: Identifier)
    separator (getter: Token?)
  ConstructorFieldInitializer (class extends Object implements ConstructorInitializer):
    equals (getter: Token)
    expression (getter: Expression)
    fieldName (getter: SimpleIdentifier)
    period (getter: Token?)
    thisKeyword (getter: Token?)
  ConstructorInitializer (class extends Object implements AstNode, sealed (immediate subtypes: AssertInitializer, ConstructorFieldInitializer, ConstructorInitializerImpl, RedirectingConstructorInvocation, SuperConstructorInvocation))
  ConstructorName (class extends Object implements AstNode, ConstructorReferenceNode):
    name (getter: SimpleIdentifier?)
    period (getter: Token?)
    type (getter: NamedType)
  ConstructorReference (class extends Object implements Expression, CommentReferableExpression):
    constructorName (getter: ConstructorName)
  ConstructorReferenceNode (class extends Object implements AstNode):
    element (getter: ConstructorElement?, experimental)
  ConstructorSelector (class extends Object implements AstNode):
    name (getter: SimpleIdentifier)
    period (getter: Token)
  ContinueStatement (class extends Object implements Statement):
    continueKeyword (getter: Token)
    label (getter: SimpleIdentifier?)
    semicolon (getter: Token)
    target (getter: AstNode?)
  DartPattern (class extends Object implements AstNode, ListPatternElement, sealed (immediate subtypes: CastPattern, ConstantPattern, DartPatternImpl, ListPattern, LogicalAndPattern, LogicalOrPattern, MapPattern, NullAssertPattern, NullCheckPattern, ObjectPattern, ParenthesizedPattern, RecordPattern, RelationalPattern, VariablePattern, WildcardPattern)):
    matchedValueType (getter: DartType?)
    precedence (getter: PatternPrecedence)
    unParenthesized (getter: DartPattern)
  Declaration (class extends Object implements AnnotatedNode):
    declaredFragment (getter: Fragment?, experimental)
  DeclaredIdentifier (class extends Object implements Declaration):
    declaredElement2 (getter: LocalVariableElement?, experimental)
    declaredFragment (getter: LocalVariableFragment?)
    isConst (getter: bool)
    isFinal (getter: bool)
    keyword (getter: Token?)
    name (getter: Token)
    type (getter: TypeAnnotation?)
  DeclaredVariablePattern (class extends Object implements VariablePattern, sealed (immediate subtypes: DeclaredVariablePatternImpl)):
    declaredElement2 (getter: BindPatternVariableElement?, experimental)
    declaredFragment (getter: BindPatternVariableFragment?)
    keyword (getter: Token?)
    type (getter: TypeAnnotation?)
  DefaultFormalParameter (class extends Object implements FormalParameter):
    defaultValue (getter: Expression?)
    parameter (getter: NormalFormalParameter)
    separator (getter: Token?)
  Directive (class extends Object implements AnnotatedNode, sealed (immediate subtypes: DirectiveImpl, LibraryDirective, PartOfDirective, UriBasedDirective))
  DoStatement (class extends Object implements Statement):
    body (getter: Statement)
    condition (getter: Expression)
    doKeyword (getter: Token)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
    semicolon (getter: Token)
    whileKeyword (getter: Token)
  DotShorthandConstructorInvocation (class extends InvocationExpression implements ConstructorReferenceNode, experimental):
    constKeyword (getter: Token?)
    constructorName (getter: SimpleIdentifier)
    isConst (getter: bool)
    period (getter: Token)
  DotShorthandInvocation (class extends InvocationExpression, experimental):
    memberName (getter: SimpleIdentifier)
    period (getter: Token)
  DotShorthandPropertyAccess (class extends Expression, experimental):
    period (getter: Token)
    propertyName (getter: SimpleIdentifier)
  DottedName (class extends Object implements AstNode):
    components (getter: NodeList<SimpleIdentifier>)
  DoubleLiteral (class extends Object implements Literal):
    literal (getter: Token)
    value (getter: double)
  EmptyFunctionBody (class extends Object implements FunctionBody):
    semicolon (getter: Token)
  EmptyStatement (class extends Object implements Statement):
    semicolon (getter: Token)
  EnumConstantArguments (class extends Object implements AstNode):
    argumentList (getter: ArgumentList)
    constructorSelector (getter: ConstructorSelector?)
    typeArguments (getter: TypeArgumentList?)
  EnumConstantDeclaration (class extends Object implements Declaration):
    arguments (getter: EnumConstantArguments?)
    augmentKeyword (getter: Token?, experimental)
    constructorElement2 (getter: ConstructorElement?, experimental)
    declaredFragment (getter: FieldFragment?, experimental)
    name (getter: Token)
  EnumDeclaration (class extends Object implements NamedCompilationUnitMember):
    augmentKeyword (getter: Token?, experimental)
    constants (getter: NodeList<EnumConstantDeclaration>)
    declaredFragment (getter: EnumFragment?, experimental)
    enumKeyword (getter: Token)
    implementsClause (getter: ImplementsClause?)
    leftBracket (getter: Token)
    members (getter: NodeList<ClassMember>)
    rightBracket (getter: Token)
    semicolon (getter: Token?)
    typeParameters (getter: TypeParameterList?)
    withClause (getter: WithClause?)
  ExportDirective (class extends Object implements NamespaceDirective):
    exportKeyword (getter: Token)
    libraryExport (getter: LibraryExport?, experimental)
  Expression (class extends Object implements CollectionElement):
    canBeConst (getter: bool)
    correspondingParameter (getter: FormalParameterElement?, experimental)
    inConstantContext (getter: bool)
    isAssignable (getter: bool)
    precedence (getter: Precedence)
    staticType (getter: DartType?)
    unParenthesized (getter: Expression)
    computeConstantValue (method: AttemptedConstantEvaluationResult? Function())
  ExpressionFunctionBody (class extends Object implements FunctionBody):
    expression (getter: Expression)
    functionDefinition (getter: Token)
    keyword (getter: Token?)
    semicolon (getter: Token?)
    star (getter: Token?)
  ExpressionStatement (class extends Object implements Statement):
    expression (getter: Expression)
    semicolon (getter: Token?)
  ExtendsClause (class extends Object implements AstNode):
    extendsKeyword (getter: Token)
    superclass (getter: NamedType)
  ExtensionDeclaration (class extends Object implements CompilationUnitMember):
    augmentKeyword (getter: Token?, experimental)
    declaredFragment (getter: ExtensionFragment?, experimental)
    extensionKeyword (getter: Token)
    leftBracket (getter: Token)
    members (getter: NodeList<ClassMember>)
    name (getter: Token?)
    onClause (getter: ExtensionOnClause?)
    rightBracket (getter: Token)
    typeKeyword (getter: Token?)
    typeParameters (getter: TypeParameterList?)
  ExtensionOnClause (class extends Object implements AstNode):
    extendedType (getter: TypeAnnotation)
    onKeyword (getter: Token)
  ExtensionOverride (class extends Object implements Expression):
    argumentList (getter: ArgumentList)
    element2 (getter: ExtensionElement, experimental)
    extendedType (getter: DartType?)
    importPrefix (getter: ImportPrefixReference?)
    isNullAware (getter: bool)
    name (getter: Token)
    typeArgumentTypes (getter: List<DartType>?)
    typeArguments (getter: TypeArgumentList?)
  ExtensionTypeDeclaration (class extends Object implements NamedCompilationUnitMember, experimental):
    augmentKeyword (getter: Token?, experimental)
    constKeyword (getter: Token?)
    declaredFragment (getter: ExtensionTypeFragment?, experimental)
    extensionKeyword (getter: Token)
    implementsClause (getter: ImplementsClause?)
    leftBracket (getter: Token)
    members (getter: NodeList<ClassMember>)
    representation (getter: RepresentationDeclaration)
    rightBracket (getter: Token)
    typeKeyword (getter: Token)
    typeParameters (getter: TypeParameterList?)
  FieldDeclaration (class extends Object implements ClassMember):
    abstractKeyword (getter: Token?)
    augmentKeyword (getter: Token?, experimental)
    covariantKeyword (getter: Token?)
    externalKeyword (getter: Token?)
    fields (getter: VariableDeclarationList)
    isStatic (getter: bool)
    semicolon (getter: Token)
    staticKeyword (getter: Token?)
  FieldFormalParameter (class extends Object implements NormalFormalParameter):
    declaredFragment (getter: FieldFormalParameterFragment?, experimental)
    keyword (getter: Token?)
    name (getter: Token)
    parameters (getter: FormalParameterList?)
    period (getter: Token)
    question (getter: Token?)
    thisKeyword (getter: Token)
    type (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  ForEachParts (class extends Object implements ForLoopParts, sealed (immediate subtypes: ForEachPartsImpl, ForEachPartsWithDeclaration, ForEachPartsWithIdentifier, ForEachPartsWithPattern)):
    inKeyword (getter: Token)
    iterable (getter: Expression)
  ForEachPartsWithDeclaration (class extends Object implements ForEachParts):
    loopVariable (getter: DeclaredIdentifier)
  ForEachPartsWithIdentifier (class extends Object implements ForEachParts):
    identifier (getter: SimpleIdentifier)
  ForEachPartsWithPattern (class extends Object implements ForEachParts):
    keyword (getter: Token)
    metadata (getter: NodeList<Annotation>)
    pattern (getter: DartPattern)
  ForElement (class extends Object implements CollectionElement, ForLoop<CollectionElement>)
  ForLoop (class<Body extends AstNode> extends Object implements AstNode, sealed (immediate subtypes: ForElement, ForLoopImpl, ForStatement)):
    awaitKeyword (getter: Token?)
    body (getter: Body)
    forKeyword (getter: Token)
    forLoopParts (getter: ForLoopParts)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  ForLoopParts (class extends Object implements AstNode, sealed (immediate subtypes: ForEachParts, ForLoopPartsImpl, ForParts)):
    parent (getter: ForLoop<AstNode>)
  ForParts (class extends Object implements ForLoopParts, sealed (immediate subtypes: ForPartsImpl, ForPartsWithDeclarations, ForPartsWithExpression, ForPartsWithPattern)):
    condition (getter: Expression?)
    leftSeparator (getter: Token)
    rightSeparator (getter: Token)
    updaters (getter: NodeList<Expression>)
  ForPartsWithDeclarations (class extends Object implements ForParts):
    variables (getter: VariableDeclarationList)
  ForPartsWithExpression (class extends Object implements ForParts):
    initialization (getter: Expression?)
  ForPartsWithPattern (class extends Object implements ForParts):
    variables (getter: PatternVariableDeclaration)
  ForStatement (class extends Object implements Statement, ForLoop<Statement>)
  FormalParameter (class extends Object implements AstNode, sealed (immediate subtypes: DefaultFormalParameter, FormalParameterImpl, NormalFormalParameter)):
    covariantKeyword (getter: Token?)
    declaredFragment (getter: FormalParameterFragment?, experimental)
    isConst (getter: bool)
    isExplicitlyTyped (getter: bool)
    isFinal (getter: bool)
    isNamed (getter: bool)
    isOptional (getter: bool)
    isOptionalNamed (getter: bool)
    isOptionalPositional (getter: bool)
    isPositional (getter: bool)
    isRequired (getter: bool)
    isRequiredNamed (getter: bool)
    isRequiredPositional (getter: bool)
    metadata (getter: NodeList<Annotation>)
    name (getter: Token?)
    requiredKeyword (getter: Token?)
  FormalParameterList (class extends Object implements AstNode):
    leftDelimiter (getter: Token?)
    leftParenthesis (getter: Token)
    parameterFragments (getter: List<FormalParameterFragment?>, experimental)
    parameters (getter: NodeList<FormalParameter>)
    rightDelimiter (getter: Token?)
    rightParenthesis (getter: Token)
  FunctionBody (class extends Object implements AstNode, sealed (immediate subtypes: BlockFunctionBody, EmptyFunctionBody, ExpressionFunctionBody, FunctionBodyImpl, NativeFunctionBody)):
    isAsynchronous (getter: bool)
    isGenerator (getter: bool)
    isSynchronous (getter: bool)
    keyword (getter: Token?)
    star (getter: Token?)
    isPotentiallyMutatedInScope2 (method: bool Function(VariableElement), experimental)
  FunctionDeclaration (class extends Object implements NamedCompilationUnitMember):
    augmentKeyword (getter: Token?, experimental)
    declaredFragment (getter: ExecutableFragment?, experimental)
    externalKeyword (getter: Token?)
    functionExpression (getter: FunctionExpression)
    isGetter (getter: bool)
    isSetter (getter: bool)
    propertyKeyword (getter: Token?)
    returnType (getter: TypeAnnotation?)
  FunctionDeclarationStatement (class extends Object implements Statement):
    functionDeclaration (getter: FunctionDeclaration)
  FunctionExpression (class extends Object implements Expression):
    body (getter: FunctionBody)
    declaredFragment (getter: ExecutableFragment?, experimental)
    parameters (getter: FormalParameterList?)
    typeParameters (getter: TypeParameterList?)
  FunctionExpressionInvocation (class extends Object implements NullShortableExpression, InvocationExpression):
    element (getter: ExecutableElement?, experimental)
    function (getter: Expression)
  FunctionReference (class extends Object implements Expression, CommentReferableExpression):
    function (getter: Expression)
    typeArgumentTypes (getter: List<DartType>?)
    typeArguments (getter: TypeArgumentList?)
  FunctionTypeAlias (class extends Object implements TypeAlias):
    declaredFragment (getter: TypeAliasFragment?, experimental)
    parameters (getter: FormalParameterList)
    returnType (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  FunctionTypedFormalParameter (class extends Object implements NormalFormalParameter):
    name (getter: Token)
    parameters (getter: FormalParameterList)
    question (getter: Token?)
    returnType (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  GenericFunctionType (class extends Object implements TypeAnnotation):
    declaredFragment (getter: GenericFunctionTypeFragment?)
    functionKeyword (getter: Token)
    parameters (getter: FormalParameterList)
    returnType (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  GenericTypeAlias (class extends Object implements TypeAlias):
    equals (getter: Token)
    functionType (getter: GenericFunctionType?)
    type (getter: TypeAnnotation)
    typeParameters (getter: TypeParameterList?)
  GuardedPattern (class extends Object implements AstNode):
    pattern (getter: DartPattern)
    whenClause (getter: WhenClause?)
  HideCombinator (class extends Object implements Combinator):
    hiddenNames (getter: NodeList<SimpleIdentifier>)
  Identifier (class extends Object implements Expression, CommentReferableExpression, sealed (immediate subtypes: IdentifierImpl, LibraryIdentifier, PrefixedIdentifier, SimpleIdentifier)):
    isPrivateName (static method: bool Function(String))
    element (getter: Element?, experimental)
    name (getter: String)
  IfElement (class extends Object implements CollectionElement):
    caseClause (getter: CaseClause?)
    elseElement (getter: CollectionElement?)
    elseKeyword (getter: Token?)
    expression (getter: Expression)
    ifKeyword (getter: Token)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
    thenElement (getter: CollectionElement)
  IfStatement (class extends Object implements Statement):
    caseClause (getter: CaseClause?)
    elseKeyword (getter: Token?)
    elseStatement (getter: Statement?)
    expression (getter: Expression)
    ifKeyword (getter: Token)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
    thenStatement (getter: Statement)
  ImplementsClause (class extends Object implements AstNode):
    implementsKeyword (getter: Token)
    interfaces (getter: NodeList<NamedType>)
  ImplicitCallReference (class extends Object implements MethodReferenceExpression):
    expression (getter: Expression)
    typeArgumentTypes (getter: List<DartType>)
    typeArguments (getter: TypeArgumentList?)
  ImportDirective (class extends Object implements NamespaceDirective):
    asKeyword (getter: Token?)
    deferredKeyword (getter: Token?)
    importKeyword (getter: Token)
    libraryImport (getter: LibraryImport?, experimental)
    prefix (getter: SimpleIdentifier?)
  ImportPrefixReference (class extends Object implements AstNode):
    element2 (getter: Element?, experimental)
    name (getter: Token)
    period (getter: Token)
  IndexExpression (class extends Object implements NullShortableExpression, MethodReferenceExpression):
    index (getter: Expression)
    isCascaded (getter: bool)
    isNullAware (getter: bool)
    leftBracket (getter: Token)
    period (getter: Token?)
    question (getter: Token?)
    realTarget (getter: Expression)
    rightBracket (getter: Token)
    target (getter: Expression?)
    inGetterContext (method: bool Function())
    inSetterContext (method: bool Function())
  InstanceCreationExpression (class extends Object implements Expression):
    argumentList (getter: ArgumentList)
    constructorName (getter: ConstructorName)
    isConst (getter: bool)
    keyword (getter: Token?)
  IntegerLiteral (class extends Object implements Literal):
    literal (getter: Token)
    value (getter: int?)
  InterpolationElement (class extends Object implements AstNode, sealed (immediate subtypes: InterpolationElementImpl, InterpolationExpression, InterpolationString))
  InterpolationExpression (class extends Object implements InterpolationElement):
    expression (getter: Expression)
    leftBracket (getter: Token)
    rightBracket (getter: Token?)
  InterpolationString (class extends Object implements InterpolationElement):
    contents (getter: Token)
    contentsEnd (getter: int)
    contentsOffset (getter: int)
    value (getter: String)
  InvocationExpression (class extends Object implements Expression):
    argumentList (getter: ArgumentList)
    function (getter: Expression)
    staticInvokeType (getter: DartType?)
    typeArgumentTypes (getter: List<DartType>?)
    typeArguments (getter: TypeArgumentList?)
  IsExpression (class extends Object implements Expression):
    expression (getter: Expression)
    isOperator (getter: Token)
    notOperator (getter: Token?)
    type (getter: TypeAnnotation)
  Label (class extends Object implements AstNode):
    colon (getter: Token)
    declaredFragment (getter: LabelFragment?)
    label (getter: SimpleIdentifier)
  LabeledStatement (class extends Object implements Statement):
    labels (getter: NodeList<Label>)
    statement (getter: Statement)
  LibraryDirective (class extends Object implements Directive):
    element2 (getter: LibraryElement?, experimental)
    libraryKeyword (getter: Token)
    name2 (getter: LibraryIdentifier?)
    semicolon (getter: Token)
  LibraryIdentifier (class extends Object implements Identifier):
    components (getter: NodeList<SimpleIdentifier>)
  ListLiteral (class extends Object implements TypedLiteral):
    elements (getter: NodeList<CollectionElement>)
    leftBracket (getter: Token)
    rightBracket (getter: Token)
  ListPattern (class extends Object implements DartPattern):
    elements (getter: NodeList<ListPatternElement>)
    leftBracket (getter: Token)
    requiredType (getter: DartType?)
    rightBracket (getter: Token)
    typeArguments (getter: TypeArgumentList?)
  ListPatternElement (class extends Object implements AstNode, sealed (immediate subtypes: DartPattern, ListPatternElementImpl, RestPatternElement))
  Literal (class extends Object implements Expression, sealed (immediate subtypes: BooleanLiteral, DoubleLiteral, IntegerLiteral, LiteralImpl, NullLiteral, RecordLiteral, StringLiteral, SymbolLiteral, TypedLiteral))
  LogicalAndPattern (class extends Object implements DartPattern):
    leftOperand (getter: DartPattern)
    operator (getter: Token)
    rightOperand (getter: DartPattern)
  LogicalOrPattern (class extends Object implements DartPattern):
    leftOperand (getter: DartPattern)
    operator (getter: Token)
    rightOperand (getter: DartPattern)
  MapLiteralEntry (class extends Object implements CollectionElement):
    key (getter: Expression)
    keyQuestion (getter: Token?)
    separator (getter: Token)
    value (getter: Expression)
    valueQuestion (getter: Token?)
  MapPattern (class extends Object implements DartPattern):
    elements (getter: NodeList<MapPatternElement>)
    leftBracket (getter: Token)
    requiredType (getter: DartType?)
    rightBracket (getter: Token)
    typeArguments (getter: TypeArgumentList?)
  MapPatternElement (class extends Object implements AstNode, sealed (immediate subtypes: MapPatternElementImpl, MapPatternEntry, RestPatternElement))
  MapPatternEntry (class extends Object implements AstNode, MapPatternElement):
    key (getter: Expression)
    separator (getter: Token)
    value (getter: DartPattern)
  MethodDeclaration (class extends Object implements ClassMember):
    augmentKeyword (getter: Token?)
    body (getter: FunctionBody)
    declaredFragment (getter: ExecutableFragment?, experimental)
    externalKeyword (getter: Token?)
    isAbstract (getter: bool)
    isGetter (getter: bool)
    isOperator (getter: bool)
    isSetter (getter: bool)
    isStatic (getter: bool)
    modifierKeyword (getter: Token?)
    name (getter: Token)
    operatorKeyword (getter: Token?)
    parameters (getter: FormalParameterList?)
    propertyKeyword (getter: Token?)
    returnType (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  MethodInvocation (class extends Object implements NullShortableExpression, InvocationExpression):
    isCascaded (getter: bool)
    isNullAware (getter: bool)
    methodName (getter: SimpleIdentifier)
    operator (getter: Token?)
    realTarget (getter: Expression?)
    target (getter: Expression?)
  MethodReferenceExpression (class extends Object implements Expression):
    element (getter: MethodElement?, experimental)
  MixinDeclaration (class extends Object implements NamedCompilationUnitMember):
    augmentKeyword (getter: Token?)
    baseKeyword (getter: Token?)
    declaredFragment (getter: MixinFragment?, experimental)
    implementsClause (getter: ImplementsClause?)
    leftBracket (getter: Token)
    members (getter: NodeList<ClassMember>)
    mixinKeyword (getter: Token)
    onClause (getter: MixinOnClause?)
    rightBracket (getter: Token)
    typeParameters (getter: TypeParameterList?)
  MixinOnClause (class extends Object implements AstNode):
    onKeyword (getter: Token)
    superclassConstraints (getter: NodeList<NamedType>)
  NamedCompilationUnitMember (class extends Object implements CompilationUnitMember):
    name (getter: Token)
  NamedExpression (class extends Object implements Expression):
    element2 (getter: FormalParameterElement?, experimental)
    expression (getter: Expression)
    name (getter: Label)
  NamedType (class extends Object implements TypeAnnotation):
    element2 (getter: Element?, experimental)
    importPrefix (getter: ImportPrefixReference?)
    isDeferred (getter: bool)
    name (getter: Token)
    name2 (getter: Token, deprecated)
    type (getter: DartType?)
    typeArguments (getter: TypeArgumentList?)
  NamespaceDirective (class extends Object implements UriBasedDirective, sealed (immediate subtypes: ExportDirective, ImportDirective, NamespaceDirectiveImpl)):
    combinators (getter: NodeList<Combinator>)
    configurations (getter: NodeList<Configuration>)
    semicolon (getter: Token)
  NativeClause (class extends Object implements AstNode):
    name (getter: StringLiteral?)
    nativeKeyword (getter: Token)
  NativeFunctionBody (class extends Object implements FunctionBody):
    nativeKeyword (getter: Token)
    semicolon (getter: Token)
    stringLiteral (getter: StringLiteral?)
  NodeList (class<E extends AstNode> extends Object implements List<E>):
    beginToken (getter: Token?)
    endToken (getter: Token?)
    length= (setter: int, deprecated)
    owner (getter: AstNode)
    [] (method: E Function(int))
    accept (method: void Function(AstVisitor<dynamic>))
    add (method: void Function(E), deprecated)
    addAll (method: void Function(Iterable<E>), deprecated)
    clear (method: void Function(), deprecated)
    insert (method: void Function(int, E), deprecated)
    removeAt (method: E Function(int), deprecated)
  NormalFormalParameter (class extends Object implements FormalParameter, AnnotatedNode, sealed (immediate subtypes: FieldFormalParameter, FunctionTypedFormalParameter, NormalFormalParameterImpl, SimpleFormalParameter, SuperFormalParameter))
  NullAssertPattern (class extends Object implements DartPattern):
    operator (getter: Token)
    pattern (getter: DartPattern)
  NullAwareElement (class extends Object implements CollectionElement):
    question (getter: Token)
    value (getter: Expression)
  NullCheckPattern (class extends Object implements DartPattern):
    operator (getter: Token)
    pattern (getter: DartPattern)
  NullLiteral (class extends Object implements Literal):
    literal (getter: Token)
  NullShortableExpression (class extends Object implements Expression):
    nullShortingTermination (getter: Expression)
  ObjectPattern (class extends Object implements DartPattern):
    fields (getter: NodeList<PatternField>)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
    type (getter: NamedType)
  ParenthesizedExpression (class extends Object implements Expression):
    expression (getter: Expression)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  ParenthesizedPattern (class extends Object implements DartPattern):
    leftParenthesis (getter: Token)
    pattern (getter: DartPattern)
    rightParenthesis (getter: Token)
  PartDirective (class extends Object implements UriBasedDirective):
    configurations (getter: NodeList<Configuration>)
    partInclude (getter: PartInclude?, experimental)
    partKeyword (getter: Token)
    semicolon (getter: Token)
  PartOfDirective (class extends Object implements Directive):
    libraryName (getter: LibraryIdentifier?)
    ofKeyword (getter: Token)
    partKeyword (getter: Token)
    semicolon (getter: Token)
    uri (getter: StringLiteral?)
  PatternAssignment (class extends Object implements Expression):
    equals (getter: Token)
    expression (getter: Expression)
    pattern (getter: DartPattern)
  PatternField (class extends Object implements AstNode):
    effectiveName (getter: String?)
    element2 (getter: Element?, experimental)
    name (getter: PatternFieldName?)
    pattern (getter: DartPattern)
  PatternFieldName (class extends Object implements AstNode):
    colon (getter: Token)
    name (getter: Token?)
  PatternVariableDeclaration (class extends Object implements AnnotatedNode):
    equals (getter: Token)
    expression (getter: Expression)
    keyword (getter: Token)
    pattern (getter: DartPattern)
  PatternVariableDeclarationStatement (class extends Object implements Statement):
    declaration (getter: PatternVariableDeclaration)
    semicolon (getter: Token)
  PostfixExpression (class extends Object implements Expression, NullShortableExpression, MethodReferenceExpression, CompoundAssignmentExpression):
    element (getter: MethodElement?)
    operand (getter: Expression)
    operator (getter: Token)
  PrefixExpression (class extends Object implements Expression, NullShortableExpression, MethodReferenceExpression, CompoundAssignmentExpression):
    element (getter: MethodElement?)
    operand (getter: Expression)
    operator (getter: Token)
  PrefixedIdentifier (class extends Object implements Identifier):
    identifier (getter: SimpleIdentifier)
    isDeferred (getter: bool)
    period (getter: Token)
    prefix (getter: SimpleIdentifier)
  PropertyAccess (class extends Object implements NullShortableExpression, CommentReferableExpression):
    isCascaded (getter: bool)
    isNullAware (getter: bool)
    operator (getter: Token)
    propertyName (getter: SimpleIdentifier)
    realTarget (getter: Expression)
    target (getter: Expression?)
  RecordLiteral (class extends Object implements Literal):
    constKeyword (getter: Token?)
    fields (getter: NodeList<Expression>)
    isConst (getter: bool)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  RecordPattern (class extends Object implements DartPattern):
    fields (getter: NodeList<PatternField>)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  RecordTypeAnnotation (class extends Object implements TypeAnnotation):
    leftParenthesis (getter: Token)
    namedFields (getter: RecordTypeAnnotationNamedFields?)
    positionalFields (getter: NodeList<RecordTypeAnnotationPositionalField>)
    rightParenthesis (getter: Token)
  RecordTypeAnnotationField (class extends Object implements AstNode, sealed (immediate subtypes: RecordTypeAnnotationFieldImpl, RecordTypeAnnotationNamedField, RecordTypeAnnotationPositionalField)):
    metadata (getter: NodeList<Annotation>)
    name (getter: Token?)
    type (getter: TypeAnnotation)
  RecordTypeAnnotationNamedField (class extends Object implements RecordTypeAnnotationField):
    name (getter: Token)
  RecordTypeAnnotationNamedFields (class extends Object implements AstNode):
    fields (getter: NodeList<RecordTypeAnnotationNamedField>)
    leftBracket (getter: Token)
    rightBracket (getter: Token)
  RecordTypeAnnotationPositionalField (class extends Object implements RecordTypeAnnotationField)
  RedirectingConstructorInvocation (class extends Object implements ConstructorInitializer, ConstructorReferenceNode):
    argumentList (getter: ArgumentList)
    constructorName (getter: SimpleIdentifier?)
    period (getter: Token?)
    thisKeyword (getter: Token)
  RelationalPattern (class extends Object implements DartPattern):
    element2 (getter: MethodElement?, experimental)
    operand (getter: Expression)
    operator (getter: Token)
  RepresentationConstructorName (class extends Object implements AstNode, experimental):
    name (getter: Token)
    period (getter: Token)
  RepresentationDeclaration (class extends Object implements AstNode, experimental):
    constructorFragment (getter: ConstructorFragment?, experimental)
    constructorName (getter: RepresentationConstructorName?)
    fieldFragment (getter: FieldFragment?, experimental)
    fieldMetadata (getter: NodeList<Annotation>)
    fieldName (getter: Token)
    fieldType (getter: TypeAnnotation)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
  RestPatternElement (class extends Object implements ListPatternElement, MapPatternElement):
    operator (getter: Token)
    pattern (getter: DartPattern?)
  RethrowExpression (class extends Object implements Expression):
    rethrowKeyword (getter: Token)
  ReturnStatement (class extends Object implements Statement):
    expression (getter: Expression?)
    returnKeyword (getter: Token)
    semicolon (getter: Token)
  ScriptTag (class extends Object implements AstNode):
    scriptTag (getter: Token)
  SetOrMapLiteral (class extends Object implements TypedLiteral):
    elements (getter: NodeList<CollectionElement>)
    isMap (getter: bool)
    isSet (getter: bool)
    leftBracket (getter: Token)
    rightBracket (getter: Token)
  ShowCombinator (class extends Object implements Combinator):
    shownNames (getter: NodeList<SimpleIdentifier>)
  SimpleFormalParameter (class extends Object implements NormalFormalParameter):
    keyword (getter: Token?)
    type (getter: TypeAnnotation?)
  SimpleIdentifier (class extends Object implements Identifier):
    isQualified (getter: bool)
    tearOffTypeArgumentTypes (getter: List<DartType>?)
    token (getter: Token)
    inDeclarationContext (method: bool Function())
    inGetterContext (method: bool Function())
    inSetterContext (method: bool Function())
  SimpleStringLiteral (class extends Object implements SingleStringLiteral):
    literal (getter: Token)
    value (getter: String)
  SingleStringLiteral (class extends Object implements StringLiteral, sealed (immediate subtypes: SimpleStringLiteral, SingleStringLiteralImpl, StringInterpolation)):
    contentsEnd (getter: int)
    contentsOffset (getter: int)
    isMultiline (getter: bool)
    isRaw (getter: bool)
    isSingleQuoted (getter: bool)
  SpreadElement (class extends Object implements CollectionElement):
    expression (getter: Expression)
    isNullAware (getter: bool)
    spreadOperator (getter: Token)
  Statement (class extends Object implements AstNode):
    unlabeled (getter: Statement)
  StringInterpolation (class extends Object implements SingleStringLiteral):
    elements (getter: NodeList<InterpolationElement>)
    firstString (getter: InterpolationString)
    lastString (getter: InterpolationString)
  StringLiteral (class extends Object implements Literal, sealed (immediate subtypes: AdjacentStrings, SingleStringLiteral, StringLiteralImpl)):
    stringValue (getter: String?)
  SuperConstructorInvocation (class extends Object implements ConstructorInitializer, ConstructorReferenceNode):
    argumentList (getter: ArgumentList)
    constructorName (getter: SimpleIdentifier?)
    period (getter: Token?)
    superKeyword (getter: Token)
  SuperExpression (class extends Object implements Expression):
    superKeyword (getter: Token)
  SuperFormalParameter (class extends Object implements NormalFormalParameter):
    keyword (getter: Token?)
    name (getter: Token)
    parameters (getter: FormalParameterList?)
    period (getter: Token)
    question (getter: Token?)
    superKeyword (getter: Token)
    type (getter: TypeAnnotation?)
    typeParameters (getter: TypeParameterList?)
  SwitchCase (class extends Object implements SwitchMember):
    expression (getter: Expression)
  SwitchDefault (class extends Object implements SwitchMember)
  SwitchExpression (class extends Object implements Expression):
    cases (getter: NodeList<SwitchExpressionCase>)
    expression (getter: Expression)
    leftBracket (getter: Token)
    leftParenthesis (getter: Token)
    rightBracket (getter: Token)
    rightParenthesis (getter: Token)
    switchKeyword (getter: Token)
  SwitchExpressionCase (class extends Object implements AstNode):
    arrow (getter: Token)
    expression (getter: Expression)
    guardedPattern (getter: GuardedPattern)
  SwitchMember (class extends Object implements AstNode, sealed (immediate subtypes: SwitchCase, SwitchDefault, SwitchMemberImpl, SwitchPatternCase)):
    colon (getter: Token)
    keyword (getter: Token)
    labels (getter: NodeList<Label>)
    statements (getter: NodeList<Statement>)
  SwitchPatternCase (class extends Object implements SwitchMember):
    guardedPattern (getter: GuardedPattern)
  SwitchStatement (class extends Object implements Statement):
    expression (getter: Expression)
    leftBracket (getter: Token)
    leftParenthesis (getter: Token)
    members (getter: NodeList<SwitchMember>)
    rightBracket (getter: Token)
    rightParenthesis (getter: Token)
    switchKeyword (getter: Token)
  SymbolLiteral (class extends Object implements Literal):
    components (getter: List<Token>)
    poundSign (getter: Token)
  ThisExpression (class extends Object implements Expression):
    thisKeyword (getter: Token)
  ThrowExpression (class extends Object implements Expression):
    expression (getter: Expression)
    throwKeyword (getter: Token)
  TopLevelVariableDeclaration (class extends Object implements CompilationUnitMember):
    augmentKeyword (getter: Token?, experimental)
    externalKeyword (getter: Token?)
    semicolon (getter: Token)
    variables (getter: VariableDeclarationList)
  TryStatement (class extends Object implements Statement):
    body (getter: Block)
    catchClauses (getter: NodeList<CatchClause>)
    finallyBlock (getter: Block?)
    finallyKeyword (getter: Token?)
    tryKeyword (getter: Token)
  TypeAlias (class extends Object implements NamedCompilationUnitMember):
    augmentKeyword (getter: Token?, experimental)
    semicolon (getter: Token)
    typedefKeyword (getter: Token)
  TypeAnnotation (class extends Object implements AstNode, sealed (immediate subtypes: GenericFunctionType, NamedType, RecordTypeAnnotation, TypeAnnotationImpl)):
    question (getter: Token?)
    type (getter: DartType?)
  TypeArgumentList (class extends Object implements AstNode):
    arguments (getter: NodeList<TypeAnnotation>)
    leftBracket (getter: Token)
    rightBracket (getter: Token)
  TypeLiteral (class extends Object implements Expression, CommentReferableExpression):
    type (getter: NamedType)
  TypeParameter (class extends Object implements Declaration):
    bound (getter: TypeAnnotation?)
    declaredFragment (getter: TypeParameterFragment?, experimental)
    extendsKeyword (getter: Token?)
    name (getter: Token)
  TypeParameterList (class extends Object implements AstNode):
    leftBracket (getter: Token)
    rightBracket (getter: Token)
    typeParameters (getter: NodeList<TypeParameter>)
  TypedLiteral (class extends Object implements Literal, sealed (immediate subtypes: ListLiteral, SetOrMapLiteral, TypedLiteralImpl)):
    constKeyword (getter: Token?)
    isConst (getter: bool)
    typeArguments (getter: TypeArgumentList?)
  UriBasedDirective (class extends Object implements Directive, sealed (immediate subtypes: NamespaceDirective, PartDirective, UriBasedDirectiveImpl)):
    uri (getter: StringLiteral)
  VariableDeclaration (class extends Object implements Declaration):
    declaredElement2 (getter: LocalVariableElement?, experimental)
    declaredFragment (getter: VariableFragment?, experimental)
    equals (getter: Token?)
    initializer (getter: Expression?)
    isConst (getter: bool)
    isFinal (getter: bool)
    isLate (getter: bool)
    name (getter: Token)
  VariableDeclarationList (class extends Object implements AnnotatedNode):
    isConst (getter: bool)
    isFinal (getter: bool)
    isLate (getter: bool)
    keyword (getter: Token?)
    lateKeyword (getter: Token?)
    type (getter: TypeAnnotation?)
    variables (getter: NodeList<VariableDeclaration>)
  VariableDeclarationStatement (class extends Object implements Statement):
    semicolon (getter: Token)
    variables (getter: VariableDeclarationList)
  VariablePattern (class extends Object implements DartPattern, sealed (immediate subtypes: AssignedVariablePattern, DeclaredVariablePattern, VariablePatternImpl)):
    name (getter: Token)
  WhenClause (class extends Object implements AstNode):
    expression (getter: Expression)
    whenKeyword (getter: Token)
  WhileStatement (class extends Object implements Statement):
    body (getter: Statement)
    condition (getter: Expression)
    leftParenthesis (getter: Token)
    rightParenthesis (getter: Token)
    whileKeyword (getter: Token)
  WildcardPattern (class extends Object implements DartPattern):
    keyword (getter: Token?)
    name (getter: Token)
    type (getter: TypeAnnotation?)
  WithClause (class extends Object implements AstNode):
    mixinTypes (getter: NodeList<NamedType>)
    withKeyword (getter: Token)
  YieldStatement (class extends Object implements Statement):
    expression (getter: Expression)
    semicolon (getter: Token)
    star (getter: Token?)
    yieldKeyword (getter: Token)
package:analyzer/dart/ast/doc_comment.dart:
  BlockDocDirective (class extends Object implements DocDirective, experimental):
    new (constructor: BlockDocDirective Function(DocDirectiveTag, DocDirectiveTag?))
    closingTag (getter: DocDirectiveTag?)
    openingTag (getter: DocDirectiveTag)
    type (getter: DocDirectiveType)
  CodeBlockType (enum, experimental):
    fenced (static getter: CodeBlockType)
    indented (static getter: CodeBlockType)
    values (static getter: List<CodeBlockType>)
  DocDirective (class extends Object, sealed (immediate subtypes: BlockDocDirective, SimpleDocDirective), experimental):
    type (getter: DocDirectiveType)
  DocDirectiveArgument (class extends Object, sealed (immediate subtypes: DocDirectiveNamedArgument, DocDirectivePositionalArgument), experimental):
    end (getter: int)
    offset (getter: int)
    value (getter: String)
  DocDirectiveNamedArgument (class extends DocDirectiveArgument, experimental):
    new (constructor: DocDirectiveNamedArgument Function({required int end, required String name, required int offset, required String value}))
    name (getter: String)
  DocDirectiveParameter (class extends Object, experimental):
    new (constructor: DocDirectiveParameter Function(String, DocDirectiveParameterFormat))
    expectedFormat (getter: DocDirectiveParameterFormat)
    name (getter: String)
  DocDirectiveParameterFormat (enum, experimental):
    any (static getter: DocDirectiveParameterFormat)
    integer (static getter: DocDirectiveParameterFormat)
    uri (static getter: DocDirectiveParameterFormat)
    values (static getter: List<DocDirectiveParameterFormat>)
    youtubeUrl (static getter: DocDirectiveParameterFormat)
    youtubeUrlPrefix (static getter: String)
    displayString (getter: String)
  DocDirectivePositionalArgument (class extends DocDirectiveArgument, experimental):
    new (constructor: DocDirectivePositionalArgument Function({required int end, required int offset, required String value}))
  DocDirectiveTag (class extends Object, experimental):
    new (constructor: DocDirectiveTag Function({required int end, required int nameEnd, required int nameOffset, required List<DocDirectiveNamedArgument> namedArguments, required int offset, required List<DocDirectiveArgument> positionalArguments, required DocDirectiveType type}))
    end (getter: int)
    nameEnd (getter: int)
    nameOffset (getter: int)
    namedArguments (getter: List<DocDirectiveNamedArgument>)
    offset (getter: int)
    positionalArguments (getter: List<DocDirectiveArgument>)
    type (getter: DocDirectiveType)
  DocDirectiveType (enum, experimental):
    animation (static getter: DocDirectiveType)
    canonicalFor (static getter: DocDirectiveType)
    category (static getter: DocDirectiveType)
    endInjectHtml (static getter: DocDirectiveType)
    endTemplate (static getter: DocDirectiveType)
    endTool (static getter: DocDirectiveType)
    injectHtml (static getter: DocDirectiveType)
    macro (static getter: DocDirectiveType)
    subCategory (static getter: DocDirectiveType)
    template (static getter: DocDirectiveType)
    tool (static getter: DocDirectiveType)
    values (static getter: List<DocDirectiveType>)
    youtube (static getter: DocDirectiveType)
    isBlock (getter: bool)
    name (getter: String)
    namedParameters (getter: List<DocDirectiveParameter>)
    opposingName (getter: String?)
    positionalParameters (getter: List<DocDirectiveParameter>)
    restParametersAllowed (getter: bool)
  DocImport (class extends Object, experimental):
    new (constructor: DocImport Function({required ImportDirective import, required int offset}))
    import (getter: ImportDirective)
    import= (setter: ImportDirective)
    offset (getter: int)
    offset= (setter: int)
  MdCodeBlock (class extends Object, experimental):
    new (constructor: MdCodeBlock Function({required String? infoString, required List<MdCodeBlockLine> lines, required CodeBlockType type}))
    infoString (getter: String?)
    lines (getter: List<MdCodeBlockLine>)
    type (getter: CodeBlockType)
  MdCodeBlockLine (class extends Object, experimental):
    new (constructor: MdCodeBlockLine Function({required int length, required int offset}))
    length (getter: int)
    offset (getter: int)
  SimpleDocDirective (class extends Object implements DocDirective, experimental):
    new (constructor: SimpleDocDirective Function(DocDirectiveTag))
    tag (getter: DocDirectiveTag)
    type (getter: DocDirectiveType)
package:analyzer/dart/ast/precedence.dart:
  PatternPrecedence (class extends Object):
    logicalAnd (static getter: PatternPrecedence)
    logicalOr (static getter: PatternPrecedence)
    postfix (static getter: PatternPrecedence)
    primary (static getter: PatternPrecedence)
    relational (static getter: PatternPrecedence)
    hashCode (getter: int)
    < (method: bool Function(PatternPrecedence))
    <= (method: bool Function(PatternPrecedence))
    == (method: bool Function(Object))
    > (method: bool Function(PatternPrecedence))
    >= (method: bool Function(PatternPrecedence))
  Precedence (class extends Object):
    additive (static getter: Precedence)
    assignment (static getter: Precedence)
    bitwiseAnd (static getter: Precedence)
    bitwiseOr (static getter: Precedence)
    bitwiseXor (static getter: Precedence)
    cascade (static getter: Precedence)
    conditional (static getter: Precedence)
    equality (static getter: Precedence)
    ifNull (static getter: Precedence)
    logicalAnd (static getter: Precedence)
    logicalOr (static getter: Precedence)
    multiplicative (static getter: Precedence)
    none (static getter: Precedence)
    postfix (static getter: Precedence)
    prefix (static getter: Precedence)
    primary (static getter: Precedence)
    relational (static getter: Precedence)
    shift (static getter: Precedence)
    forTokenType (constructor: Precedence Function(TokenType))
    hashCode (getter: int)
    < (method: bool Function(Precedence))
    <= (method: bool Function(Precedence))
    == (method: bool Function(Object))
    > (method: bool Function(Precedence))
    >= (method: bool Function(Precedence))
package:analyzer/dart/ast/syntactic_entity.dart:
  SyntacticEntity (class extends Object):
    new (constructor: SyntacticEntity Function())
    end (getter: int)
    length (getter: int)
    offset (getter: int)
package:analyzer/dart/ast/token.dart:
  CommentToken (class extends StringToken):
    new (constructor: CommentToken Function(TokenType, String, int))
    parent (getter: SimpleToken?)
    parent= (setter: SimpleToken?)
  Keyword (class extends TokenType):
    ABSTRACT (static getter: Keyword)
    AS (static getter: Keyword)
    ASSERT (static getter: Keyword)
    ASYNC (static getter: Keyword)
    AUGMENT (static getter: Keyword)
    AWAIT (static getter: Keyword)
    BASE (static getter: Keyword)
    BREAK (static getter: Keyword)
    CASE (static getter: Keyword)
    CATCH (static getter: Keyword)
    CLASS (static getter: Keyword)
    CONST (static getter: Keyword)
    CONTINUE (static getter: Keyword)
    COVARIANT (static getter: Keyword)
    DEFAULT (static getter: Keyword)
    DEFERRED (static getter: Keyword)
    DO (static getter: Keyword)
    DYNAMIC (static getter: Keyword)
    ELSE (static getter: Keyword)
    ENUM (static getter: Keyword)
    EXPORT (static getter: Keyword)
    EXTENDS (static getter: Keyword)
    EXTENSION (static getter: Keyword)
    EXTERNAL (static getter: Keyword)
    FACTORY (static getter: Keyword)
    FALSE (static getter: Keyword)
    FINAL (static getter: Keyword)
    FINALLY (static getter: Keyword)
    FOR (static getter: Keyword)
    FUNCTION (static getter: Keyword)
    GET (static getter: Keyword)
    HIDE (static getter: Keyword)
    IF (static getter: Keyword)
    IMPLEMENTS (static getter: Keyword)
    IMPORT (static getter: Keyword)
    IN (static getter: Keyword)
    INOUT (static getter: Keyword)
    INTERFACE (static getter: Keyword)
    IS (static getter: Keyword)
    LATE (static getter: Keyword)
    LIBRARY (static getter: Keyword)
    MIXIN (static getter: Keyword)
    NATIVE (static getter: Keyword)
    NEW (static getter: Keyword)
    NULL (static getter: Keyword)
    OF (static getter: Keyword)
    ON (static getter: Keyword)
    OPERATOR (static getter: Keyword)
    OUT (static getter: Keyword)
    PART (static getter: Keyword)
    PATCH (static getter: Keyword)
    REQUIRED (static getter: Keyword)
    RETHROW (static getter: Keyword)
    RETURN (static getter: Keyword)
    SEALED (static getter: Keyword)
    SET (static getter: Keyword)
    SHOW (static getter: Keyword)
    SOURCE (static getter: Keyword)
    STATIC (static getter: Keyword)
    SUPER (static getter: Keyword)
    SWITCH (static getter: Keyword)
    SYNC (static getter: Keyword)
    THIS (static getter: Keyword)
    THROW (static getter: Keyword)
    TRUE (static getter: Keyword)
    TRY (static getter: Keyword)
    TYPEDEF (static getter: Keyword)
    VAR (static getter: Keyword)
    VOID (static getter: Keyword)
    WHEN (static getter: Keyword)
    WHILE (static getter: Keyword)
    WITH (static getter: Keyword)
    YIELD (static getter: Keyword)
    keywords (static getter: Map<String, Keyword>)
    values (static getter: List<Keyword>)
    new (constructor: Keyword Function(int, String, String, KeywordStyle, {bool isModifier, bool isTopLevelKeyword, int precedence}))
    isBuiltIn (getter: bool)
    isBuiltInOrPseudo (getter: bool)
    isPseudo (getter: bool)
    isReservedWord (getter: bool)
    keywordStyle (getter: KeywordStyle)
    name (getter: String)
    toString (method: String Function())
  LanguageVersionToken (class extends CommentToken):
    from (constructor: LanguageVersionToken Function(String, int, int, int))
    major (getter: int)
    minor (getter: int)
  Token (class extends Object implements SyntacticEntity):
    lexicallyFirst (static method: Token? Function([Token?, Token?, Token?, Token?, Token?]))
    eof (constructor: Token Function(int, [CommentToken?]))
    new (constructor: Token Function(TokenType, int, [CommentToken?]))
    beforeSynthetic (getter: Token?)
    beforeSynthetic= (setter: Token?)
    charCount (getter: int)
    charEnd (getter: int)
    charOffset (getter: int)
    end (getter: int)
    endGroup (getter: Token?)
    isEof (getter: bool)
    isIdentifier (getter: bool)
    isKeyword (getter: bool)
    isKeywordOrIdentifier (getter: bool)
    isModifier (getter: bool)
    isOperator (getter: bool)
    isSynthetic (getter: bool)
    isTopLevelKeyword (getter: bool)
    isUserDefinableOperator (getter: bool)
    keyword (getter: Keyword?)
    kind (getter: int)
    length (getter: int)
    lexeme (getter: String)
    next (getter: Token?)
    next= (setter: Token?)
    offset (getter: int)
    offset= (setter: int)
    precedingComments (getter: CommentToken?)
    previous (getter: Token?)
    previous= (setter: Token?)
    stringValue (getter: String?)
    type (getter: TokenType)
    typeIndex (getter: int)
    matchesAny (method: bool Function(List<TokenType>))
    setNext (method: Token Function(Token))
    setNextWithoutSettingPrevious (method: Token? Function(Token?))
    toString (method: String Function())
    value (method: Object Function())
  TokenType (class extends Object):
    AMPERSAND (static getter: TokenType)
    AMPERSAND_AMPERSAND (static getter: TokenType)
    AMPERSAND_AMPERSAND_EQ (static getter: TokenType)
    AMPERSAND_EQ (static getter: TokenType)
    AS (static getter: TokenType)
    AT (static getter: TokenType)
    BACKPING (static getter: TokenType)
    BACKSLASH (static getter: TokenType)
    BAD_INPUT (static getter: TokenType)
    BANG (static getter: TokenType)
    BANG_EQ (static getter: TokenType)
    BANG_EQ_EQ (static getter: TokenType)
    BAR (static getter: TokenType)
    BAR_BAR (static getter: TokenType)
    BAR_BAR_EQ (static getter: TokenType)
    BAR_EQ (static getter: TokenType)
    CARET (static getter: TokenType)
    CARET_EQ (static getter: TokenType)
    CLOSE_CURLY_BRACKET (static getter: TokenType)
    CLOSE_PAREN (static getter: TokenType)
    CLOSE_SQUARE_BRACKET (static getter: TokenType)
    COLON (static getter: TokenType)
    COMMA (static getter: TokenType)
    DOUBLE (static getter: TokenType)
    DOUBLE_WITH_SEPARATORS (static getter: TokenType)
    EOF (static getter: TokenType)
    EQ (static getter: TokenType)
    EQ_EQ (static getter: TokenType)
    EQ_EQ_EQ (static getter: TokenType)
    FUNCTION (static getter: TokenType)
    GT (static getter: TokenType)
    GT_EQ (static getter: TokenType)
    GT_GT (static getter: TokenType)
    GT_GT_EQ (static getter: TokenType)
    GT_GT_GT (static getter: TokenType)
    GT_GT_GT_EQ (static getter: TokenType)
    HASH (static getter: TokenType)
    HEXADECIMAL (static getter: TokenType)
    HEXADECIMAL_WITH_SEPARATORS (static getter: TokenType)
    IDENTIFIER (static getter: TokenType)
    INDEX (static getter: TokenType)
    INDEX_EQ (static getter: TokenType)
    INT (static getter: TokenType)
    INT_WITH_SEPARATORS (static getter: TokenType)
    IS (static getter: TokenType)
    LT (static getter: TokenType)
    LT_EQ (static getter: TokenType)
    LT_LT (static getter: TokenType)
    LT_LT_EQ (static getter: TokenType)
    MINUS (static getter: TokenType)
    MINUS_EQ (static getter: TokenType)
    MINUS_MINUS (static getter: TokenType)
    MULTI_LINE_COMMENT (static getter: TokenType)
    OPEN_CURLY_BRACKET (static getter: TokenType)
    OPEN_PAREN (static getter: TokenType)
    OPEN_SQUARE_BRACKET (static getter: TokenType)
    PERCENT (static getter: TokenType)
    PERCENT_EQ (static getter: TokenType)
    PERIOD (static getter: TokenType)
    PERIOD_PERIOD (static getter: TokenType)
    PERIOD_PERIOD_PERIOD (static getter: TokenType)
    PERIOD_PERIOD_PERIOD_QUESTION (static getter: TokenType)
    PLUS (static getter: TokenType)
    PLUS_EQ (static getter: TokenType)
    PLUS_PLUS (static getter: TokenType)
    QUESTION (static getter: TokenType)
    QUESTION_PERIOD (static getter: TokenType)
    QUESTION_PERIOD_PERIOD (static getter: TokenType)
    QUESTION_QUESTION (static getter: TokenType)
    QUESTION_QUESTION_EQ (static getter: TokenType)
    RECOVERY (static getter: TokenType)
    SCRIPT_TAG (static getter: TokenType)
    SEMICOLON (static getter: TokenType)
    SINGLE_LINE_COMMENT (static getter: TokenType)
    SLASH (static getter: TokenType)
    SLASH_EQ (static getter: TokenType)
    STAR (static getter: TokenType)
    STAR_EQ (static getter: TokenType)
    STRING (static getter: TokenType)
    STRING_INTERPOLATION_EXPRESSION (static getter: TokenType)
    STRING_INTERPOLATION_IDENTIFIER (static getter: TokenType)
    TILDE (static getter: TokenType)
    TILDE_SLASH (static getter: TokenType)
    TILDE_SLASH_EQ (static getter: TokenType)
    UNUSED (static getter: TokenType)
    all (static getter: List<TokenType>)
    new (constructor: TokenType Function(int, String, String, int, int, {TokenType? binaryOperatorOfCompoundAssignment, bool isBinaryOperator, bool isModifier, bool isOperator, bool isTopLevelKeyword, bool isUserDefinableOperator, bool stringValueShouldBeNull}))
    binaryOperatorOfCompoundAssignment (getter: TokenType?)
    index (getter: int)
    isAdditiveOperator (getter: bool)
    isAssignmentOperator (getter: bool)
    isAssociativeOperator (getter: bool)
    isBinaryOperator (getter: bool)
    isBuiltIn (getter: bool)
    isEqualityOperator (getter: bool)
    isIncrementOperator (getter: bool)
    isKeyword (getter: bool)
    isModifier (getter: bool)
    isMultiplicativeOperator (getter: bool)
    isOperator (getter: bool)
    isPseudo (getter: bool)
    isRelationalOperator (getter: bool)
    isReservedWord (getter: bool)
    isSelectorOperator (getter: bool)
    isShiftOperator (getter: bool)
    isTopLevelKeyword (getter: bool)
    isUnaryPostfixOperator (getter: bool)
    isUnaryPrefixOperator (getter: bool)
    isUserDefinableOperator (getter: bool)
    kind (getter: int)
    lexeme (getter: String)
    name (getter: String)
    precedence (getter: int)
    stringValue (getter: String?)
    toString (method: String Function())
package:analyzer/dart/ast/visitor.dart:
  BreadthFirstVisitor (class<R> extends GeneralizingAstVisitor<R>):
    new (constructor: BreadthFirstVisitor<R> Function())
    visitAllNodes (method: void Function(AstNode))
    visitNode (method: R? Function(AstNode))
  DelegatingAstVisitor (class<T> extends UnifyingAstVisitor<T>):
    new (constructor: DelegatingAstVisitor<T> Function(Iterable<AstVisitor<T>>))
    delegates (getter: Iterable<AstVisitor<T>>)
    visitNode (method: T? Function(AstNode))
  GeneralizingAstVisitor (class<R> extends Object implements AstVisitor<R>):
    new (constructor: GeneralizingAstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotatedNode (method: R? Function(AnnotatedNode))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassMember (method: R? Function(ClassMember))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitCollectionElement (method: R? Function(CollectionElement))
    visitCombinator (method: R? Function(Combinator))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitCompilationUnitMember (method: R? Function(CompilationUnitMember))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorInitializer (method: R? Function(ConstructorInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDartPattern (method: R? Function(DartPattern))
    visitDeclaration (method: R? Function(Declaration))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDirective (method: R? Function(Directive))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpression (method: R? Function(Expression))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachParts (method: R? Function(ForEachParts))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForParts (method: R? Function(ForParts))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameter (method: R? Function(FormalParameter))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionBody (method: R? Function(FunctionBody))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIdentifier (method: R? Function(Identifier))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationElement (method: R? Function(InterpolationElement))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitInvocationExpression (method: R? Function(InvocationExpression))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLiteral (method: R? Function(Literal))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedCompilationUnitMember (method: R? Function(NamedCompilationUnitMember))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNamespaceDirective (method: R? Function(NamespaceDirective))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNode (method: R? Function(AstNode))
    visitNormalFormalParameter (method: R? Function(NormalFormalParameter))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationField (method: R? Function(RecordTypeAnnotationField))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSingleStringLiteral (method: R? Function(SingleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStatement (method: R? Function(Statement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitStringLiteral (method: R? Function(StringLiteral))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchMember (method: R? Function(SwitchMember))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeAlias (method: R? Function(TypeAlias))
    visitTypeAnnotation (method: R? Function(TypeAnnotation))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitTypedLiteral (method: R? Function(TypedLiteral))
    visitUriBasedDirective (method: R? Function(UriBasedDirective))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
  RecursiveAstVisitor (class<R> extends Object implements AstVisitor<R>):
    new (constructor: RecursiveAstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
  SimpleAstVisitor (class<R> extends Object implements AstVisitor<R>):
    new (constructor: SimpleAstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
  ThrowingAstVisitor (class<R> extends Object implements AstVisitor<R>):
    new (constructor: ThrowingAstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
  TimedAstVisitor (class<T> extends Object implements AstVisitor<T>):
    new (constructor: TimedAstVisitor<T> Function(AstVisitor<T>, [Stopwatch?]))
    stopwatch (getter: Stopwatch)
    visitAdjacentStrings (method: T? Function(AdjacentStrings))
    visitAnnotation (method: T? Function(Annotation))
    visitArgumentList (method: T? Function(ArgumentList))
    visitAsExpression (method: T? Function(AsExpression))
    visitAssertInitializer (method: T? Function(AssertInitializer))
    visitAssertStatement (method: T? Function(AssertStatement))
    visitAssignedVariablePattern (method: T? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: T? Function(AssignmentExpression))
    visitAugmentedExpression (method: T? Function(AugmentedExpression))
    visitAugmentedInvocation (method: T? Function(AugmentedInvocation))
    visitAwaitExpression (method: T? Function(AwaitExpression))
    visitBinaryExpression (method: T? Function(BinaryExpression))
    visitBlock (method: T? Function(Block))
    visitBlockFunctionBody (method: T? Function(BlockFunctionBody))
    visitBooleanLiteral (method: T? Function(BooleanLiteral))
    visitBreakStatement (method: T? Function(BreakStatement))
    visitCascadeExpression (method: T? Function(CascadeExpression))
    visitCaseClause (method: T? Function(CaseClause))
    visitCastPattern (method: T? Function(CastPattern))
    visitCatchClause (method: T? Function(CatchClause))
    visitCatchClauseParameter (method: T? Function(CatchClauseParameter))
    visitClassDeclaration (method: T? Function(ClassDeclaration))
    visitClassTypeAlias (method: T? Function(ClassTypeAlias))
    visitComment (method: T? Function(Comment))
    visitCommentReference (method: T? Function(CommentReference))
    visitCompilationUnit (method: T? Function(CompilationUnit))
    visitConditionalExpression (method: T? Function(ConditionalExpression))
    visitConfiguration (method: T? Function(Configuration))
    visitConstantPattern (method: T? Function(ConstantPattern))
    visitConstructorDeclaration (method: T? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: T? Function(ConstructorFieldInitializer))
    visitConstructorName (method: T? Function(ConstructorName))
    visitConstructorReference (method: T? Function(ConstructorReference))
    visitConstructorSelector (method: T? Function(ConstructorSelector))
    visitContinueStatement (method: T? Function(ContinueStatement))
    visitDeclaredIdentifier (method: T? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: T? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: T? Function(DefaultFormalParameter))
    visitDoStatement (method: T? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: T? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: T? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: T? Function(DotShorthandPropertyAccess))
    visitDottedName (method: T? Function(DottedName))
    visitDoubleLiteral (method: T? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: T? Function(EmptyFunctionBody))
    visitEmptyStatement (method: T? Function(EmptyStatement))
    visitEnumConstantArguments (method: T? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: T? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: T? Function(EnumDeclaration))
    visitExportDirective (method: T? Function(ExportDirective))
    visitExpressionFunctionBody (method: T? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: T? Function(ExpressionStatement))
    visitExtendsClause (method: T? Function(ExtendsClause))
    visitExtensionDeclaration (method: T? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: T? Function(ExtensionOnClause))
    visitExtensionOverride (method: T? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: T? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: T? Function(FieldDeclaration))
    visitFieldFormalParameter (method: T? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: T? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: T? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: T? Function(ForEachPartsWithPattern))
    visitForElement (method: T? Function(ForElement))
    visitForPartsWithDeclarations (method: T? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: T? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: T? Function(ForPartsWithPattern))
    visitForStatement (method: T? Function(ForStatement))
    visitFormalParameterList (method: T? Function(FormalParameterList))
    visitFunctionDeclaration (method: T? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: T? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: T? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: T? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: T? Function(FunctionReference))
    visitFunctionTypeAlias (method: T? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: T? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: T? Function(GenericFunctionType))
    visitGenericTypeAlias (method: T? Function(GenericTypeAlias))
    visitGuardedPattern (method: T? Function(GuardedPattern))
    visitHideCombinator (method: T? Function(HideCombinator))
    visitIfElement (method: T? Function(IfElement))
    visitIfStatement (method: T? Function(IfStatement))
    visitImplementsClause (method: T? Function(ImplementsClause))
    visitImplicitCallReference (method: T? Function(ImplicitCallReference))
    visitImportDirective (method: T? Function(ImportDirective))
    visitImportPrefixReference (method: T? Function(ImportPrefixReference))
    visitIndexExpression (method: T? Function(IndexExpression))
    visitInstanceCreationExpression (method: T? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: T? Function(IntegerLiteral))
    visitInterpolationExpression (method: T? Function(InterpolationExpression))
    visitInterpolationString (method: T? Function(InterpolationString))
    visitIsExpression (method: T? Function(IsExpression))
    visitLabel (method: T? Function(Label))
    visitLabeledStatement (method: T? Function(LabeledStatement))
    visitLibraryDirective (method: T? Function(LibraryDirective))
    visitLibraryIdentifier (method: T? Function(LibraryIdentifier))
    visitListLiteral (method: T? Function(ListLiteral))
    visitListPattern (method: T? Function(ListPattern))
    visitLogicalAndPattern (method: T? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: T? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: T? Function(MapLiteralEntry))
    visitMapPattern (method: T? Function(MapPattern))
    visitMapPatternEntry (method: T? Function(MapPatternEntry))
    visitMethodDeclaration (method: T? Function(MethodDeclaration))
    visitMethodInvocation (method: T? Function(MethodInvocation))
    visitMixinDeclaration (method: T? Function(MixinDeclaration))
    visitMixinOnClause (method: T? Function(MixinOnClause))
    visitNamedExpression (method: T? Function(NamedExpression))
    visitNamedType (method: T? Function(NamedType))
    visitNativeClause (method: T? Function(NativeClause))
    visitNativeFunctionBody (method: T? Function(NativeFunctionBody))
    visitNullAssertPattern (method: T? Function(NullAssertPattern))
    visitNullAwareElement (method: T? Function(NullAwareElement))
    visitNullCheckPattern (method: T? Function(NullCheckPattern))
    visitNullLiteral (method: T? Function(NullLiteral))
    visitObjectPattern (method: T? Function(ObjectPattern))
    visitParenthesizedExpression (method: T? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: T? Function(ParenthesizedPattern))
    visitPartDirective (method: T? Function(PartDirective))
    visitPartOfDirective (method: T? Function(PartOfDirective))
    visitPatternAssignment (method: T? Function(PatternAssignment))
    visitPatternField (method: T? Function(PatternField))
    visitPatternFieldName (method: T? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: T? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: T? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: T? Function(PostfixExpression))
    visitPrefixExpression (method: T? Function(PrefixExpression))
    visitPrefixedIdentifier (method: T? Function(PrefixedIdentifier))
    visitPropertyAccess (method: T? Function(PropertyAccess))
    visitRecordLiteral (method: T? Function(RecordLiteral))
    visitRecordPattern (method: T? Function(RecordPattern))
    visitRecordTypeAnnotation (method: T? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: T? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: T? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: T? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: T? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: T? Function(RelationalPattern))
    visitRepresentationConstructorName (method: T? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: T? Function(RepresentationDeclaration))
    visitRestPatternElement (method: T? Function(RestPatternElement))
    visitRethrowExpression (method: T? Function(RethrowExpression))
    visitReturnStatement (method: T? Function(ReturnStatement))
    visitScriptTag (method: T? Function(ScriptTag))
    visitSetOrMapLiteral (method: T? Function(SetOrMapLiteral))
    visitShowCombinator (method: T? Function(ShowCombinator))
    visitSimpleFormalParameter (method: T? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: T? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: T? Function(SimpleStringLiteral))
    visitSpreadElement (method: T? Function(SpreadElement))
    visitStringInterpolation (method: T? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: T? Function(SuperConstructorInvocation))
    visitSuperExpression (method: T? Function(SuperExpression))
    visitSuperFormalParameter (method: T? Function(SuperFormalParameter))
    visitSwitchCase (method: T? Function(SwitchCase))
    visitSwitchDefault (method: T? Function(SwitchDefault))
    visitSwitchExpression (method: T? Function(SwitchExpression))
    visitSwitchExpressionCase (method: T? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: T? Function(SwitchPatternCase))
    visitSwitchStatement (method: T? Function(SwitchStatement))
    visitSymbolLiteral (method: T? Function(SymbolLiteral))
    visitThisExpression (method: T? Function(ThisExpression))
    visitThrowExpression (method: T? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: T? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: T? Function(TryStatement))
    visitTypeArgumentList (method: T? Function(TypeArgumentList))
    visitTypeLiteral (method: T? Function(TypeLiteral))
    visitTypeParameter (method: T? Function(TypeParameter))
    visitTypeParameterList (method: T? Function(TypeParameterList))
    visitVariableDeclaration (method: T? Function(VariableDeclaration))
    visitVariableDeclarationList (method: T? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: T? Function(VariableDeclarationStatement))
    visitWhenClause (method: T? Function(WhenClause))
    visitWhileStatement (method: T? Function(WhileStatement))
    visitWildcardPattern (method: T? Function(WildcardPattern))
    visitWithClause (method: T? Function(WithClause))
    visitYieldStatement (method: T? Function(YieldStatement))
  UnifyingAstVisitor (class<R> extends Object implements AstVisitor<R>):
    new (constructor: UnifyingAstVisitor<R> Function())
    visitAdjacentStrings (method: R? Function(AdjacentStrings))
    visitAnnotation (method: R? Function(Annotation))
    visitArgumentList (method: R? Function(ArgumentList))
    visitAsExpression (method: R? Function(AsExpression))
    visitAssertInitializer (method: R? Function(AssertInitializer))
    visitAssertStatement (method: R? Function(AssertStatement))
    visitAssignedVariablePattern (method: R? Function(AssignedVariablePattern))
    visitAssignmentExpression (method: R? Function(AssignmentExpression))
    visitAugmentedExpression (method: R? Function(AugmentedExpression))
    visitAugmentedInvocation (method: R? Function(AugmentedInvocation))
    visitAwaitExpression (method: R? Function(AwaitExpression))
    visitBinaryExpression (method: R? Function(BinaryExpression))
    visitBlock (method: R? Function(Block))
    visitBlockFunctionBody (method: R? Function(BlockFunctionBody))
    visitBooleanLiteral (method: R? Function(BooleanLiteral))
    visitBreakStatement (method: R? Function(BreakStatement))
    visitCascadeExpression (method: R? Function(CascadeExpression))
    visitCaseClause (method: R? Function(CaseClause))
    visitCastPattern (method: R? Function(CastPattern))
    visitCatchClause (method: R? Function(CatchClause))
    visitCatchClauseParameter (method: R? Function(CatchClauseParameter))
    visitClassDeclaration (method: R? Function(ClassDeclaration))
    visitClassTypeAlias (method: R? Function(ClassTypeAlias))
    visitComment (method: R? Function(Comment))
    visitCommentReference (method: R? Function(CommentReference))
    visitCompilationUnit (method: R? Function(CompilationUnit))
    visitConditionalExpression (method: R? Function(ConditionalExpression))
    visitConfiguration (method: R? Function(Configuration))
    visitConstantPattern (method: R? Function(ConstantPattern))
    visitConstructorDeclaration (method: R? Function(ConstructorDeclaration))
    visitConstructorFieldInitializer (method: R? Function(ConstructorFieldInitializer))
    visitConstructorName (method: R? Function(ConstructorName))
    visitConstructorReference (method: R? Function(ConstructorReference))
    visitConstructorSelector (method: R? Function(ConstructorSelector))
    visitContinueStatement (method: R? Function(ContinueStatement))
    visitDeclaredIdentifier (method: R? Function(DeclaredIdentifier))
    visitDeclaredVariablePattern (method: R? Function(DeclaredVariablePattern))
    visitDefaultFormalParameter (method: R? Function(DefaultFormalParameter))
    visitDoStatement (method: R? Function(DoStatement))
    visitDotShorthandConstructorInvocation (method: R? Function(DotShorthandConstructorInvocation))
    visitDotShorthandInvocation (method: R? Function(DotShorthandInvocation))
    visitDotShorthandPropertyAccess (method: R? Function(DotShorthandPropertyAccess))
    visitDottedName (method: R? Function(DottedName))
    visitDoubleLiteral (method: R? Function(DoubleLiteral))
    visitEmptyFunctionBody (method: R? Function(EmptyFunctionBody))
    visitEmptyStatement (method: R? Function(EmptyStatement))
    visitEnumConstantArguments (method: R? Function(EnumConstantArguments))
    visitEnumConstantDeclaration (method: R? Function(EnumConstantDeclaration))
    visitEnumDeclaration (method: R? Function(EnumDeclaration))
    visitExportDirective (method: R? Function(ExportDirective))
    visitExpressionFunctionBody (method: R? Function(ExpressionFunctionBody))
    visitExpressionStatement (method: R? Function(ExpressionStatement))
    visitExtendsClause (method: R? Function(ExtendsClause))
    visitExtensionDeclaration (method: R? Function(ExtensionDeclaration))
    visitExtensionOnClause (method: R? Function(ExtensionOnClause))
    visitExtensionOverride (method: R? Function(ExtensionOverride))
    visitExtensionTypeDeclaration (method: R? Function(ExtensionTypeDeclaration))
    visitFieldDeclaration (method: R? Function(FieldDeclaration))
    visitFieldFormalParameter (method: R? Function(FieldFormalParameter))
    visitForEachPartsWithDeclaration (method: R? Function(ForEachPartsWithDeclaration))
    visitForEachPartsWithIdentifier (method: R? Function(ForEachPartsWithIdentifier))
    visitForEachPartsWithPattern (method: R? Function(ForEachPartsWithPattern))
    visitForElement (method: R? Function(ForElement))
    visitForPartsWithDeclarations (method: R? Function(ForPartsWithDeclarations))
    visitForPartsWithExpression (method: R? Function(ForPartsWithExpression))
    visitForPartsWithPattern (method: R? Function(ForPartsWithPattern))
    visitForStatement (method: R? Function(ForStatement))
    visitFormalParameterList (method: R? Function(FormalParameterList))
    visitFunctionDeclaration (method: R? Function(FunctionDeclaration))
    visitFunctionDeclarationStatement (method: R? Function(FunctionDeclarationStatement))
    visitFunctionExpression (method: R? Function(FunctionExpression))
    visitFunctionExpressionInvocation (method: R? Function(FunctionExpressionInvocation))
    visitFunctionReference (method: R? Function(FunctionReference))
    visitFunctionTypeAlias (method: R? Function(FunctionTypeAlias))
    visitFunctionTypedFormalParameter (method: R? Function(FunctionTypedFormalParameter))
    visitGenericFunctionType (method: R? Function(GenericFunctionType))
    visitGenericTypeAlias (method: R? Function(GenericTypeAlias))
    visitGuardedPattern (method: R? Function(GuardedPattern))
    visitHideCombinator (method: R? Function(HideCombinator))
    visitIfElement (method: R? Function(IfElement))
    visitIfStatement (method: R? Function(IfStatement))
    visitImplementsClause (method: R? Function(ImplementsClause))
    visitImplicitCallReference (method: R? Function(ImplicitCallReference))
    visitImportDirective (method: R? Function(ImportDirective))
    visitImportPrefixReference (method: R? Function(ImportPrefixReference))
    visitIndexExpression (method: R? Function(IndexExpression))
    visitInstanceCreationExpression (method: R? Function(InstanceCreationExpression))
    visitIntegerLiteral (method: R? Function(IntegerLiteral))
    visitInterpolationExpression (method: R? Function(InterpolationExpression))
    visitInterpolationString (method: R? Function(InterpolationString))
    visitIsExpression (method: R? Function(IsExpression))
    visitLabel (method: R? Function(Label))
    visitLabeledStatement (method: R? Function(LabeledStatement))
    visitLibraryDirective (method: R? Function(LibraryDirective))
    visitLibraryIdentifier (method: R? Function(LibraryIdentifier))
    visitListLiteral (method: R? Function(ListLiteral))
    visitListPattern (method: R? Function(ListPattern))
    visitLogicalAndPattern (method: R? Function(LogicalAndPattern))
    visitLogicalOrPattern (method: R? Function(LogicalOrPattern))
    visitMapLiteralEntry (method: R? Function(MapLiteralEntry))
    visitMapPattern (method: R? Function(MapPattern))
    visitMapPatternEntry (method: R? Function(MapPatternEntry))
    visitMethodDeclaration (method: R? Function(MethodDeclaration))
    visitMethodInvocation (method: R? Function(MethodInvocation))
    visitMixinDeclaration (method: R? Function(MixinDeclaration))
    visitMixinOnClause (method: R? Function(MixinOnClause))
    visitNamedExpression (method: R? Function(NamedExpression))
    visitNamedType (method: R? Function(NamedType))
    visitNativeClause (method: R? Function(NativeClause))
    visitNativeFunctionBody (method: R? Function(NativeFunctionBody))
    visitNode (method: R? Function(AstNode))
    visitNullAssertPattern (method: R? Function(NullAssertPattern))
    visitNullAwareElement (method: R? Function(NullAwareElement))
    visitNullCheckPattern (method: R? Function(NullCheckPattern))
    visitNullLiteral (method: R? Function(NullLiteral))
    visitObjectPattern (method: R? Function(ObjectPattern))
    visitParenthesizedExpression (method: R? Function(ParenthesizedExpression))
    visitParenthesizedPattern (method: R? Function(ParenthesizedPattern))
    visitPartDirective (method: R? Function(PartDirective))
    visitPartOfDirective (method: R? Function(PartOfDirective))
    visitPatternAssignment (method: R? Function(PatternAssignment))
    visitPatternField (method: R? Function(PatternField))
    visitPatternFieldName (method: R? Function(PatternFieldName))
    visitPatternVariableDeclaration (method: R? Function(PatternVariableDeclaration))
    visitPatternVariableDeclarationStatement (method: R? Function(PatternVariableDeclarationStatement))
    visitPostfixExpression (method: R? Function(PostfixExpression))
    visitPrefixExpression (method: R? Function(PrefixExpression))
    visitPrefixedIdentifier (method: R? Function(PrefixedIdentifier))
    visitPropertyAccess (method: R? Function(PropertyAccess))
    visitRecordLiteral (method: R? Function(RecordLiteral))
    visitRecordPattern (method: R? Function(RecordPattern))
    visitRecordTypeAnnotation (method: R? Function(RecordTypeAnnotation))
    visitRecordTypeAnnotationNamedField (method: R? Function(RecordTypeAnnotationNamedField))
    visitRecordTypeAnnotationNamedFields (method: R? Function(RecordTypeAnnotationNamedFields))
    visitRecordTypeAnnotationPositionalField (method: R? Function(RecordTypeAnnotationPositionalField))
    visitRedirectingConstructorInvocation (method: R? Function(RedirectingConstructorInvocation))
    visitRelationalPattern (method: R? Function(RelationalPattern))
    visitRepresentationConstructorName (method: R? Function(RepresentationConstructorName))
    visitRepresentationDeclaration (method: R? Function(RepresentationDeclaration))
    visitRestPatternElement (method: R? Function(RestPatternElement))
    visitRethrowExpression (method: R? Function(RethrowExpression))
    visitReturnStatement (method: R? Function(ReturnStatement))
    visitScriptTag (method: R? Function(ScriptTag))
    visitSetOrMapLiteral (method: R? Function(SetOrMapLiteral))
    visitShowCombinator (method: R? Function(ShowCombinator))
    visitSimpleFormalParameter (method: R? Function(SimpleFormalParameter))
    visitSimpleIdentifier (method: R? Function(SimpleIdentifier))
    visitSimpleStringLiteral (method: R? Function(SimpleStringLiteral))
    visitSpreadElement (method: R? Function(SpreadElement))
    visitStringInterpolation (method: R? Function(StringInterpolation))
    visitSuperConstructorInvocation (method: R? Function(SuperConstructorInvocation))
    visitSuperExpression (method: R? Function(SuperExpression))
    visitSuperFormalParameter (method: R? Function(SuperFormalParameter))
    visitSwitchCase (method: R? Function(SwitchCase))
    visitSwitchDefault (method: R? Function(SwitchDefault))
    visitSwitchExpression (method: R? Function(SwitchExpression))
    visitSwitchExpressionCase (method: R? Function(SwitchExpressionCase))
    visitSwitchPatternCase (method: R? Function(SwitchPatternCase))
    visitSwitchStatement (method: R? Function(SwitchStatement))
    visitSymbolLiteral (method: R? Function(SymbolLiteral))
    visitThisExpression (method: R? Function(ThisExpression))
    visitThrowExpression (method: R? Function(ThrowExpression))
    visitTopLevelVariableDeclaration (method: R? Function(TopLevelVariableDeclaration))
    visitTryStatement (method: R? Function(TryStatement))
    visitTypeArgumentList (method: R? Function(TypeArgumentList))
    visitTypeLiteral (method: R? Function(TypeLiteral))
    visitTypeParameter (method: R? Function(TypeParameter))
    visitTypeParameterList (method: R? Function(TypeParameterList))
    visitVariableDeclaration (method: R? Function(VariableDeclaration))
    visitVariableDeclarationList (method: R? Function(VariableDeclarationList))
    visitVariableDeclarationStatement (method: R? Function(VariableDeclarationStatement))
    visitWhenClause (method: R? Function(WhenClause))
    visitWhileStatement (method: R? Function(WhileStatement))
    visitWildcardPattern (method: R? Function(WildcardPattern))
    visitWithClause (method: R? Function(WithClause))
    visitYieldStatement (method: R? Function(YieldStatement))
package:analyzer/dart/constant/value.dart:
  DartObject (class extends Object):
    new (constructor: DartObject Function())
    hasKnownValue (getter: bool)
    isNull (getter: bool)
    type (getter: DartType?)
    variable2 (getter: VariableElement?)
    getField (method: DartObject? Function(String))
    toBoolValue (method: bool? Function())
    toDoubleValue (method: double? Function())
    toFunctionValue2 (method: ExecutableElement? Function(), experimental)
    toIntValue (method: int? Function())
    toListValue (method: List<DartObject>? Function())
    toMapValue (method: Map<DartObject?, DartObject?>? Function())
    toRecordValue (method: ({Map<String, DartObject> named, List<DartObject> positional})? Function())
    toSetValue (method: Set<DartObject>? Function())
    toStringValue (method: String? Function())
    toSymbolValue (method: String? Function())
    toTypeValue (method: DartType? Function())
package:analyzer/dart/element/element.dart:
  Annotatable (class extends Object):
    new (constructor: Annotatable Function())
    documentationComment (getter: String?)
    metadata (getter: Metadata)
    metadata2 (getter: Metadata, deprecated)
  BindPatternVariableElement (class extends Object implements PatternVariableElement):
    new (constructor: BindPatternVariableElement Function())
    firstFragment (getter: BindPatternVariableFragment)
    fragments (getter: List<BindPatternVariableFragment>)
  BindPatternVariableFragment (class extends Object implements PatternVariableFragment):
    new (constructor: BindPatternVariableFragment Function())
    element (getter: BindPatternVariableElement)
    nextFragment (getter: BindPatternVariableFragment?)
    previousFragment (getter: BindPatternVariableFragment?)
  ClassElement (class extends Object implements InterfaceElement):
    new (constructor: ClassElement Function())
    firstFragment (getter: ClassFragment)
    fragments (getter: List<ClassFragment>)
    hasNonFinalField (getter: bool)
    isAbstract (getter: bool)
    isBase (getter: bool)
    isConstructable (getter: bool)
    isDartCoreEnum (getter: bool)
    isDartCoreObject (getter: bool)
    isExhaustive (getter: bool)
    isFinal (getter: bool)
    isInterface (getter: bool)
    isMixinApplication (getter: bool)
    isMixinClass (getter: bool)
    isSealed (getter: bool)
    isValidMixin (getter: bool)
    isExtendableIn (method: bool Function(LibraryElement))
    isExtendableIn2 (method: bool Function(LibraryElement), deprecated)
    isImplementableIn (method: bool Function(LibraryElement))
    isImplementableIn2 (method: bool Function(LibraryElement), deprecated)
    isMixableIn (method: bool Function(LibraryElement))
    isMixableIn2 (method: bool Function(LibraryElement), deprecated)
  ClassFragment (class extends Object implements InterfaceFragment):
    new (constructor: ClassFragment Function())
    element (getter: ClassElement)
    nextFragment (getter: ClassFragment?)
    previousFragment (getter: ClassFragment?)
  ConstantInitializer (class extends Object):
    new (constructor: ConstantInitializer Function())
    expression (getter: Expression)
    fragment (getter: VariableFragment)
    evaluate (method: DartObject? Function())
  ConstructorElement (class extends Object implements ExecutableElement, HasSinceSdkVersion):
    new (constructor: ConstructorElement Function())
    baseElement (getter: ConstructorElement)
    enclosingElement (getter: InterfaceElement)
    enclosingElement2 (getter: InterfaceElement, deprecated)
    firstFragment (getter: ConstructorFragment)
    fragments (getter: List<ConstructorFragment>)
    isConst (getter: bool)
    isDefaultConstructor (getter: bool)
    isFactory (getter: bool)
    isGenerative (getter: bool)
    name3 (getter: String?)
    redirectedConstructor2 (getter: ConstructorElement?)
    returnType (getter: InterfaceType)
    superConstructor2 (getter: ConstructorElement?)
  ConstructorFragment (class extends Object implements ExecutableFragment):
    new (constructor: ConstructorFragment Function())
    element (getter: ConstructorElement)
    enclosingFragment (getter: InstanceFragment?)
    name2 (getter: String)
    nextFragment (getter: ConstructorFragment?)
    offset (getter: int)
    periodOffset (getter: int?)
    previousFragment (getter: ConstructorFragment?)
    typeName (getter: String?)
    typeNameOffset (getter: int?)
  DirectiveUri (class extends Object):
    new (constructor: DirectiveUri Function())
  DirectiveUriWithLibrary (class extends DirectiveUriWithSource):
    new (constructor: DirectiveUriWithLibrary Function())
    library2 (getter: LibraryElement)
  DirectiveUriWithRelativeUri (class extends DirectiveUriWithRelativeUriString):
    new (constructor: DirectiveUriWithRelativeUri Function())
    relativeUri (getter: Uri)
  DirectiveUriWithRelativeUriString (class extends DirectiveUri):
    new (constructor: DirectiveUriWithRelativeUriString Function())
    relativeUriString (getter: String)
  DirectiveUriWithSource (class extends DirectiveUriWithRelativeUri):
    new (constructor: DirectiveUriWithSource Function())
    source (getter: Source)
  DirectiveUriWithUnit (class extends DirectiveUriWithSource):
    new (constructor: DirectiveUriWithUnit Function())
    libraryFragment (getter: LibraryFragment, experimental)
  Element (class extends Object):
    new (constructor: Element Function())
    baseElement (getter: Element)
    children2 (getter: List<Element>)
    displayName (getter: String)
    enclosingElement (getter: Element?)
    enclosingElement2 (getter: Element?, deprecated)
    firstFragment (getter: Fragment)
    fragments (getter: List<Fragment>)
    id (getter: int)
    isPrivate (getter: bool)
    isPublic (getter: bool)
    isSynthetic (getter: bool)
    kind (getter: ElementKind)
    library (getter: LibraryElement?)
    library2 (getter: LibraryElement?, deprecated)
    lookupName (getter: String?)
    name3 (getter: String?)
    nonSynthetic (getter: Element)
    nonSynthetic2 (getter: Element, deprecated)
    session (getter: AnalysisSession?)
    accept (method: T? Function<T>(ElementVisitor2<T>))
    accept2 (method: T? Function<T>(ElementVisitor2<T>), deprecated)
    displayString2 (method: String Function({bool multiline, bool preferTypeAlias}))
    getExtendedDisplayName2 (method: String Function({String? shortName}))
    isAccessibleIn2 (method: bool Function(LibraryElement))
    thisOrAncestorMatching2 (method: Element? Function(bool Function(Element)))
    thisOrAncestorOfType2 (method: E? Function<E extends Element>())
    visitChildren2 (method: void Function<T>(ElementVisitor2<T>))
  ElementAnnotation (class extends Object):
    new (constructor: ElementAnnotation Function())
    constantEvaluationErrors (getter: List<Diagnostic>?)
    element2 (getter: Element?)
    isAlwaysThrows (getter: bool)
    isAwaitNotRequired (getter: bool)
    isDeprecated (getter: bool)
    isDoNotStore (getter: bool)
    isDoNotSubmit (getter: bool)
    isExperimental (getter: bool)
    isFactory (getter: bool)
    isImmutable (getter: bool)
    isInternal (getter: bool)
    isIsTest (getter: bool)
    isIsTestGroup (getter: bool)
    isJS (getter: bool)
    isLiteral (getter: bool)
    isMustBeConst (getter: bool)
    isMustBeOverridden (getter: bool)
    isMustCallSuper (getter: bool)
    isNonVirtual (getter: bool)
    isOptionalTypeArgs (getter: bool)
    isOverride (getter: bool)
    isProtected (getter: bool)
    isProxy (getter: bool)
    isRedeclare (getter: bool)
    isReopen (getter: bool)
    isRequired (getter: bool)
    isSealed (getter: bool)
    isTarget (getter: bool)
    isUseResult (getter: bool)
    isVisibleForOverriding (getter: bool)
    isVisibleForTemplate (getter: bool)
    isVisibleForTesting (getter: bool)
    isVisibleOutsideTemplate (getter: bool)
    isWidgetFactory (getter: bool)
    libraryFragment (getter: LibraryFragment)
    computeConstantValue (method: DartObject? Function())
    toSource (method: String Function())
  ElementDirective (class extends Object implements Annotatable):
    new (constructor: ElementDirective Function())
    libraryFragment (getter: LibraryFragment)
    uri (getter: DirectiveUri)
  ElementKind (class extends Object implements Comparable<ElementKind>):
    AUGMENTATION_IMPORT (static getter: ElementKind)
    CLASS (static getter: ElementKind)
    CLASS_AUGMENTATION (static getter: ElementKind)
    COMPILATION_UNIT (static getter: ElementKind)
    CONSTRUCTOR (static getter: ElementKind)
    DYNAMIC (static getter: ElementKind)
    ENUM (static getter: ElementKind)
    ERROR (static getter: ElementKind)
    EXPORT (static getter: ElementKind)
    EXTENSION (static getter: ElementKind)
    EXTENSION_TYPE (static getter: ElementKind)
    FIELD (static getter: ElementKind)
    FUNCTION (static getter: ElementKind)
    FUNCTION_TYPE_ALIAS (static getter: ElementKind)
    GENERIC_FUNCTION_TYPE (static getter: ElementKind)
    GETTER (static getter: ElementKind)
    IMPORT (static getter: ElementKind)
    LABEL (static getter: ElementKind)
    LIBRARY (static getter: ElementKind)
    LIBRARY_AUGMENTATION (static getter: ElementKind)
    LOCAL_VARIABLE (static getter: ElementKind)
    METHOD (static getter: ElementKind)
    MIXIN (static getter: ElementKind)
    NAME (static getter: ElementKind)
    NEVER (static getter: ElementKind)
    PARAMETER (static getter: ElementKind)
    PART (static getter: ElementKind)
    PREFIX (static getter: ElementKind)
    RECORD (static getter: ElementKind)
    SETTER (static getter: ElementKind)
    TOP_LEVEL_VARIABLE (static getter: ElementKind)
    TYPE_ALIAS (static getter: ElementKind)
    TYPE_PARAMETER (static getter: ElementKind)
    UNIVERSE (static getter: ElementKind)
    values (static getter: List<ElementKind>)
    new (constructor: ElementKind Function(String, int, String))
    displayName (getter: String)
    name (getter: String)
    ordinal (getter: int)
    compareTo (method: int Function(ElementKind))
    toString (method: String Function())
  ElementVisitor2 (class<R> extends Object):
    new (constructor: ElementVisitor2<R> Function())
    visitClassElement (method: R? Function(ClassElement))
    visitConstructorElement (method: R? Function(ConstructorElement))
    visitEnumElement (method: R? Function(EnumElement))
    visitExtensionElement (method: R? Function(ExtensionElement))
    visitExtensionTypeElement (method: R? Function(ExtensionTypeElement))
    visitFieldElement (method: R? Function(FieldElement))
    visitFieldFormalParameterElement (method: R? Function(FieldFormalParameterElement))
    visitFormalParameterElement (method: R? Function(FormalParameterElement))
    visitGenericFunctionTypeElement (method: R? Function(GenericFunctionTypeElement))
    visitGetterElement (method: R? Function(GetterElement))
    visitLabelElement (method: R? Function(LabelElement))
    visitLibraryElement (method: R? Function(LibraryElement))
    visitLocalFunctionElement (method: R? Function(LocalFunctionElement))
    visitLocalVariableElement (method: R? Function(LocalVariableElement))
    visitMethodElement (method: R? Function(MethodElement))
    visitMixinElement (method: R? Function(MixinElement))
    visitMultiplyDefinedElement (method: R? Function(MultiplyDefinedElement))
    visitPrefixElement (method: R? Function(PrefixElement))
    visitSetterElement (method: R? Function(SetterElement))
    visitSuperFormalParameterElement (method: R? Function(SuperFormalParameterElement))
    visitTopLevelFunctionElement (method: R? Function(TopLevelFunctionElement))
    visitTopLevelVariableElement (method: R? Function(TopLevelVariableElement))
    visitTypeAliasElement (method: R? Function(TypeAliasElement))
    visitTypeParameterElement (method: R? Function(TypeParameterElement))
  EnumElement (class extends Object implements InterfaceElement):
    new (constructor: EnumElement Function())
    constants2 (getter: List<FieldElement>)
    firstFragment (getter: EnumFragment)
    fragments (getter: List<EnumFragment>)
  EnumFragment (class extends Object implements InterfaceFragment):
    new (constructor: EnumFragment Function())
    constants2 (getter: List<FieldElement>)
    element (getter: EnumElement)
    nextFragment (getter: EnumFragment?)
    previousFragment (getter: EnumFragment?)
  ExecutableElement (class extends Object implements FunctionTypedElement):
    new (constructor: ExecutableElement Function())
    baseElement (getter: ExecutableElement)
    firstFragment (getter: ExecutableFragment)
    fragments (getter: List<ExecutableFragment>)
    hasImplicitReturnType (getter: bool)
    isAbstract (getter: bool)
    isExtensionTypeMember (getter: bool)
    isExternal (getter: bool)
    isStatic (getter: bool)
  ExecutableFragment (class extends Object implements FunctionTypedFragment):
    new (constructor: ExecutableFragment Function())
    element (getter: ExecutableElement)
    isAsynchronous (getter: bool)
    isAugmentation (getter: bool)
    isGenerator (getter: bool)
    isSynchronous (getter: bool)
    isSynthetic (getter: bool)
    libraryFragment (getter: LibraryFragment)
    nextFragment (getter: ExecutableFragment?)
    previousFragment (getter: ExecutableFragment?)
  ExtensionElement (class extends Object implements InstanceElement):
    new (constructor: ExtensionElement Function())
    extendedType (getter: DartType)
    firstFragment (getter: ExtensionFragment)
    fragments (getter: List<ExtensionFragment>)
  ExtensionFragment (class extends Object implements InstanceFragment):
    new (constructor: ExtensionFragment Function())
    element (getter: ExtensionElement)
    nextFragment (getter: ExtensionFragment?)
    offset (getter: int)
    previousFragment (getter: ExtensionFragment?)
  ExtensionTypeElement (class extends Object implements InterfaceElement):
    new (constructor: ExtensionTypeElement Function())
    firstFragment (getter: ExtensionTypeFragment)
    fragments (getter: List<ExtensionTypeFragment>)
    primaryConstructor (getter: ConstructorElement)
    primaryConstructor2 (getter: ConstructorElement, deprecated)
    representation (getter: FieldElement)
    representation2 (getter: FieldElement, deprecated)
    typeErasure (getter: DartType)
  ExtensionTypeFragment (class extends Object implements InterfaceFragment):
    new (constructor: ExtensionTypeFragment Function())
    element (getter: ExtensionTypeElement)
    nextFragment (getter: ExtensionTypeFragment?)
    previousFragment (getter: ExtensionTypeFragment?)
    primaryConstructor (getter: ConstructorFragment)
    primaryConstructor2 (getter: ConstructorFragment, deprecated)
    representation (getter: FieldFragment)
    representation2 (getter: FieldFragment, deprecated)
  FieldElement (class extends Object implements PropertyInducingElement):
    new (constructor: FieldElement Function())
    baseElement (getter: FieldElement)
    enclosingElement (getter: InstanceElement)
    enclosingElement2 (getter: InstanceElement, deprecated)
    firstFragment (getter: FieldFragment)
    fragments (getter: List<FieldFragment>)
    isAbstract (getter: bool)
    isCovariant (getter: bool)
    isEnumConstant (getter: bool)
    isExternal (getter: bool)
    isPromotable (getter: bool)
  FieldFormalParameterElement (class extends Object implements FormalParameterElement):
    new (constructor: FieldFormalParameterElement Function())
    field2 (getter: FieldElement?)
    firstFragment (getter: FieldFormalParameterFragment)
    fragments (getter: List<FieldFormalParameterFragment>)
  FieldFormalParameterFragment (class extends Object implements FormalParameterFragment):
    new (constructor: FieldFormalParameterFragment Function())
    element (getter: FieldFormalParameterElement)
    nextFragment (getter: FieldFormalParameterFragment?)
    previousFragment (getter: FieldFormalParameterFragment?)
  FieldFragment (class extends Object implements PropertyInducingFragment):
    new (constructor: FieldFragment Function())
    element (getter: FieldElement)
    nextFragment (getter: FieldFragment?)
    offset (getter: int)
    previousFragment (getter: FieldFragment?)
  FormalParameterElement (class extends Object implements PromotableElement, Annotatable, HasSinceSdkVersion, LocalElement):
    new (constructor: FormalParameterElement Function())
    baseElement (getter: FormalParameterElement)
    defaultValueCode (getter: String?)
    firstFragment (getter: FormalParameterFragment)
    formalParameters (getter: List<FormalParameterElement>)
    fragments (getter: List<FormalParameterFragment>)
    hasDefaultValue (getter: bool)
    isCovariant (getter: bool)
    isInitializingFormal (getter: bool)
    isNamed (getter: bool)
    isOptional (getter: bool)
    isOptionalNamed (getter: bool)
    isOptionalPositional (getter: bool)
    isPositional (getter: bool)
    isRequired (getter: bool)
    isRequiredNamed (getter: bool)
    isRequiredPositional (getter: bool)
    isSuperFormal (getter: bool)
    typeParameters2 (getter: List<TypeParameterElement>)
    appendToWithoutDelimiters2 (method: void Function(StringBuffer))
  FormalParameterFragment (class extends Object implements PromotableFragment, Annotatable, LocalFragment):
    new (constructor: FormalParameterFragment Function())
    element (getter: FormalParameterElement)
    nextFragment (getter: FormalParameterFragment?)
    offset (getter: int)
    previousFragment (getter: FormalParameterFragment?)
  Fragment (class extends Object):
    new (constructor: Fragment Function())
    children3 (getter: List<Fragment>)
    element (getter: Element)
    enclosingFragment (getter: Fragment?)
    libraryFragment (getter: LibraryFragment?)
    name2 (getter: String?)
    nameOffset2 (getter: int?)
    nextFragment (getter: Fragment?)
    offset (getter: int)
    previousFragment (getter: Fragment?)
  FunctionTypedElement (class extends Object implements TypeParameterizedElement):
    new (constructor: FunctionTypedElement Function())
    firstFragment (getter: FunctionTypedFragment)
    formalParameters (getter: List<FormalParameterElement>)
    fragments (getter: List<FunctionTypedFragment>)
    returnType (getter: DartType)
    type (getter: FunctionType)
  FunctionTypedFragment (class extends Object implements TypeParameterizedFragment):
    new (constructor: FunctionTypedFragment Function())
    element (getter: FunctionTypedElement)
    formalParameters (getter: List<FormalParameterFragment>)
    nextFragment (getter: FunctionTypedFragment?)
    previousFragment (getter: FunctionTypedFragment?)
  GenericFunctionTypeElement (class extends Object implements FunctionTypedElement):
    new (constructor: GenericFunctionTypeElement Function())
    firstFragment (getter: GenericFunctionTypeFragment)
    fragments (getter: List<GenericFunctionTypeFragment>)
  GenericFunctionTypeFragment (class extends Object implements FunctionTypedFragment):
    new (constructor: GenericFunctionTypeFragment Function())
    element (getter: GenericFunctionTypeElement)
    nextFragment (getter: GenericFunctionTypeFragment?)
    offset (getter: int)
    previousFragment (getter: GenericFunctionTypeFragment?)
  GetterElement (class extends Object implements PropertyAccessorElement):
    new (constructor: GetterElement Function())
    baseElement (getter: GetterElement)
    correspondingSetter2 (getter: SetterElement?)
    firstFragment (getter: GetterFragment)
    fragments (getter: List<GetterFragment>)
  GetterFragment (class extends Object implements PropertyAccessorFragment):
    new (constructor: GetterFragment Function())
    element (getter: GetterElement)
    nextFragment (getter: GetterFragment?)
    offset (getter: int)
    previousFragment (getter: GetterFragment?)
  HasSinceSdkVersion (class extends Object):
    new (constructor: HasSinceSdkVersion Function())
    sinceSdkVersion (getter: Version?)
  HideElementCombinator (class extends Object implements NamespaceCombinator):
    new (constructor: HideElementCombinator Function())
    hiddenNames (getter: List<String>)
  InstanceElement (class extends Object implements TypeDefiningElement, TypeParameterizedElement, HasSinceSdkVersion):
    new (constructor: InstanceElement Function())
    baseElement (getter: InstanceElement)
    enclosingElement (getter: LibraryElement)
    enclosingElement2 (getter: LibraryElement, deprecated)
    fields (getter: List<FieldElement>)
    fields2 (getter: List<FieldElement>, deprecated)
    firstFragment (getter: InstanceFragment)
    fragments (getter: List<InstanceFragment>)
    getters (getter: List<GetterElement>)
    getters2 (getter: List<GetterElement>, deprecated)
    methods (getter: List<MethodElement>)
    methods2 (getter: List<MethodElement>, deprecated)
    setters (getter: List<SetterElement>)
    setters2 (getter: List<SetterElement>, deprecated)
    thisType (getter: DartType)
    getField (method: FieldElement? Function(String))
    getField2 (method: FieldElement? Function(String), deprecated)
    getGetter (method: GetterElement? Function(String))
    getGetter2 (method: GetterElement? Function(String), deprecated)
    getMethod (method: MethodElement? Function(String))
    getMethod2 (method: MethodElement? Function(String), deprecated)
    getSetter (method: SetterElement? Function(String))
    getSetter2 (method: SetterElement? Function(String), deprecated)
    lookUpGetter (method: GetterElement? Function({required LibraryElement library, required String name}))
    lookUpGetter2 (method: GetterElement? Function({required LibraryElement library, required String name}), deprecated)
    lookUpMethod (method: MethodElement? Function({required LibraryElement library, required String name}))
    lookUpMethod2 (method: MethodElement? Function({required LibraryElement library, required String name}), deprecated)
    lookUpSetter (method: SetterElement? Function({required LibraryElement library, required String name}))
    lookUpSetter2 (method: SetterElement? Function({required LibraryElement library, required String name}), deprecated)
  InstanceFragment (class extends Object implements TypeDefiningFragment, TypeParameterizedFragment):
    new (constructor: InstanceFragment Function())
    element (getter: InstanceElement)
    enclosingFragment (getter: LibraryFragment?)
    fields (getter: List<FieldFragment>)
    fields2 (getter: List<FieldFragment>, deprecated)
    getters (getter: List<GetterFragment>)
    isAugmentation (getter: bool)
    libraryFragment (getter: LibraryFragment)
    methods (getter: List<MethodFragment>)
    methods2 (getter: List<MethodFragment>, deprecated)
    nextFragment (getter: InstanceFragment?)
    previousFragment (getter: InstanceFragment?)
    setters (getter: List<SetterFragment>)
  InterfaceElement (class extends Object implements InstanceElement):
    new (constructor: InterfaceElement Function())
    allSupertypes (getter: List<InterfaceType>)
    constructors (getter: List<ConstructorElement>)
    constructors2 (getter: List<ConstructorElement>, deprecated)
    firstFragment (getter: InterfaceFragment)
    fragments (getter: List<InterfaceFragment>)
    inheritedConcreteMembers (getter: Map<Name, ExecutableElement>)
    inheritedMembers (getter: Map<Name, ExecutableElement>)
    interfaceMembers (getter: Map<Name, ExecutableElement>)
    interfaces (getter: List<InterfaceType>)
    mixins (getter: List<InterfaceType>)
    supertype (getter: InterfaceType?)
    thisType (getter: InterfaceType)
    unnamedConstructor2 (getter: ConstructorElement?)
    getInheritedConcreteMember (method: ExecutableElement? Function(Name))
    getInheritedMember (method: ExecutableElement? Function(Name))
    getInterfaceMember (method: ExecutableElement? Function(Name))
    getNamedConstructor2 (method: ConstructorElement? Function(String))
    getOverridden (method: List<ExecutableElement>? Function(Name))
    instantiate (method: InterfaceType Function({required NullabilitySuffix nullabilitySuffix, required List<DartType> typeArguments}))
    lookUpConcreteMethod (method: MethodElement? Function(String, LibraryElement))
    lookUpInheritedMethod2 (method: MethodElement? Function({required LibraryElement library, required String methodName}))
  InterfaceFragment (class extends Object implements InstanceFragment):
    new (constructor: InterfaceFragment Function())
    constructors (getter: List<ConstructorFragment>)
    constructors2 (getter: List<ConstructorFragment>, deprecated)
    element (getter: InterfaceElement)
    interfaces (getter: List<InterfaceType>)
    mixins (getter: List<InterfaceType>)
    nextFragment (getter: InterfaceFragment?)
    previousFragment (getter: InterfaceFragment?)
    supertype (getter: InterfaceType?)
  JoinPatternVariableElement (class extends Object implements PatternVariableElement):
    new (constructor: JoinPatternVariableElement Function())
    firstFragment (getter: JoinPatternVariableFragment)
    fragments (getter: List<JoinPatternVariableFragment>)
    isConsistent (getter: bool)
    variables (getter: List<PatternVariableElement>)
    variables2 (getter: List<PatternVariableElement>, deprecated)
  JoinPatternVariableFragment (class extends Object implements PatternVariableFragment):
    new (constructor: JoinPatternVariableFragment Function())
    element (getter: JoinPatternVariableElement)
    isConsistent (getter: bool)
    nextFragment (getter: JoinPatternVariableFragment?)
    offset (getter: int)
    previousFragment (getter: JoinPatternVariableFragment?)
    variables2 (getter: List<PatternVariableFragment>)
  LabelElement (class extends Object implements Element):
    new (constructor: LabelElement Function())
    enclosingElement (getter: ExecutableElement?)
    enclosingElement2 (getter: ExecutableElement?, deprecated)
    firstFragment (getter: LabelFragment)
    fragments (getter: List<LabelFragment>)
    library (getter: LibraryElement)
    library2 (getter: LibraryElement, deprecated)
  LabelFragment (class extends Object implements Fragment):
    new (constructor: LabelFragment Function())
    element (getter: LabelElement)
    nextFragment (getter: LabelFragment?)
    previousFragment (getter: LabelFragment?)
  LibraryElement (class extends Object implements Element, Annotatable, HasSinceSdkVersion):
    new (constructor: LibraryElement Function())
    classes (getter: List<ClassElement>)
    entryPoint2 (getter: TopLevelFunctionElement?)
    enums (getter: List<EnumElement>)
    exportNamespace (getter: Namespace)
    exportedLibraries2 (getter: List<LibraryElement>)
    extensionTypes (getter: List<ExtensionTypeElement>)
    extensions (getter: List<ExtensionElement>)
    featureSet (getter: FeatureSet)
    firstFragment (getter: LibraryFragment)
    fragments (getter: List<LibraryFragment>)
    getters (getter: List<GetterElement>)
    identifier (getter: String)
    isDartAsync (getter: bool)
    isDartCore (getter: bool)
    isInSdk (getter: bool)
    languageVersion (getter: LibraryLanguageVersion)
    library (getter: LibraryElement)
    library2 (getter: LibraryElement, deprecated)
    loadLibraryFunction (getter: TopLevelFunctionElement)
    loadLibraryFunction2 (getter: TopLevelFunctionElement, deprecated)
    mixins (getter: List<MixinElement>)
    publicNamespace (getter: Namespace)
    session (getter: AnalysisSession)
    setters (getter: List<SetterElement>)
    topLevelFunctions (getter: List<TopLevelFunctionElement>)
    topLevelVariables (getter: List<TopLevelVariableElement>)
    typeAliases (getter: List<TypeAliasElement>)
    typeProvider (getter: TypeProvider)
    typeSystem (getter: TypeSystem)
    uri (getter: Uri)
    getClass2 (method: ClassElement? Function(String))
    getEnum2 (method: EnumElement? Function(String))
    getExtension (method: ExtensionElement? Function(String))
    getExtensionType (method: ExtensionTypeElement? Function(String))
    getGetter (method: GetterElement? Function(String))
    getMixin2 (method: MixinElement? Function(String))
    getSetter (method: SetterElement? Function(String))
    getTopLevelFunction (method: TopLevelFunctionElement? Function(String))
    getTopLevelVariable (method: TopLevelVariableElement? Function(String))
    getTypeAlias (method: TypeAliasElement? Function(String))
  LibraryExport (class extends Object implements ElementDirective):
    new (constructor: LibraryExport Function())
    combinators (getter: List<NamespaceCombinator>)
    exportKeywordOffset (getter: int)
    exportedLibrary2 (getter: LibraryElement?)
  LibraryFragment (class extends Object implements Fragment):
    new (constructor: LibraryFragment Function())
    accessibleExtensions2 (getter: List<ExtensionElement>)
    classes2 (getter: List<ClassFragment>)
    element (getter: LibraryElement)
    enclosingFragment (getter: LibraryFragment?)
    enums2 (getter: List<EnumFragment>)
    extensionTypes2 (getter: List<ExtensionTypeFragment>)
    extensions2 (getter: List<ExtensionFragment>)
    functions2 (getter: List<TopLevelFunctionFragment>)
    getters (getter: List<GetterFragment>)
    importedLibraries2 (getter: List<LibraryElement>)
    libraryExports2 (getter: List<LibraryExport>)
    libraryImports2 (getter: List<LibraryImport>)
    lineInfo (getter: LineInfo)
    mixins2 (getter: List<MixinFragment>)
    nextFragment (getter: LibraryFragment?)
    offset (getter: int)
    partIncludes (getter: List<PartInclude>)
    prefixes (getter: List<PrefixElement>)
    previousFragment (getter: LibraryFragment?)
    scope (getter: Scope)
    setters (getter: List<SetterFragment>)
    source (getter: Source)
    topLevelVariables2 (getter: List<TopLevelVariableFragment>)
    typeAliases2 (getter: List<TypeAliasFragment>)
  LibraryImport (class extends Object implements ElementDirective):
    new (constructor: LibraryImport Function())
    combinators (getter: List<NamespaceCombinator>)
    importKeywordOffset (getter: int)
    importedLibrary2 (getter: LibraryElement?)
    isSynthetic (getter: bool)
    namespace (getter: Namespace)
    prefix2 (getter: PrefixFragment?)
  LibraryLanguageVersion (class extends Object):
    new (constructor: LibraryLanguageVersion Function({required Version? override, required Version package}))
    effective (getter: Version)
    override (getter: Version?)
    package (getter: Version)
  LocalElement (class extends Object implements Element):
    new (constructor: LocalElement Function())
  LocalFragment (class extends Object implements Fragment):
    new (constructor: LocalFragment Function())
  LocalFunctionElement (class extends Object implements ExecutableElement, LocalElement):
    new (constructor: LocalFunctionElement Function())
    firstFragment (getter: LocalFunctionFragment)
    fragments (getter: List<LocalFunctionFragment>)
  LocalFunctionFragment (class extends Object implements ExecutableFragment, LocalFragment):
    new (constructor: LocalFunctionFragment Function())
    element (getter: LocalFunctionElement)
    nextFragment (getter: LocalFunctionFragment?)
    offset (getter: int)
    previousFragment (getter: LocalFunctionFragment?)
  LocalVariableElement (class extends Object implements PromotableElement, LocalElement, Annotatable):
    new (constructor: LocalVariableElement Function())
    baseElement (getter: LocalVariableElement)
    firstFragment (getter: LocalVariableFragment)
    fragments (getter: List<LocalVariableFragment>)
    hasInitializer (getter: bool)
  LocalVariableFragment (class extends Object implements PromotableFragment, LocalFragment):
    new (constructor: LocalVariableFragment Function())
    element (getter: LocalVariableElement)
    hasInitializer (getter: bool)
    nameOffset (getter: int)
    nextFragment (getter: LocalVariableFragment?)
    previousFragment (getter: LocalVariableFragment?)
  Metadata (class extends Object):
    new (constructor: Metadata Function())
    annotations (getter: List<ElementAnnotation>)
    hasAlwaysThrows (getter: bool)
    hasAwaitNotRequired (getter: bool)
    hasDeprecated (getter: bool)
    hasDoNotStore (getter: bool)
    hasDoNotSubmit (getter: bool)
    hasExperimental (getter: bool)
    hasFactory (getter: bool)
    hasImmutable (getter: bool)
    hasInternal (getter: bool)
    hasIsTest (getter: bool)
    hasIsTestGroup (getter: bool)
    hasJS (getter: bool)
    hasLiteral (getter: bool)
    hasMustBeConst (getter: bool)
    hasMustBeOverridden (getter: bool)
    hasMustCallSuper (getter: bool)
    hasNonVirtual (getter: bool)
    hasOptionalTypeArgs (getter: bool)
    hasOverride (getter: bool)
    hasProtected (getter: bool)
    hasRedeclare (getter: bool)
    hasReopen (getter: bool)
    hasRequired (getter: bool)
    hasSealed (getter: bool)
    hasUseResult (getter: bool)
    hasVisibleForOverriding (getter: bool)
    hasVisibleForTemplate (getter: bool)
    hasVisibleForTesting (getter: bool)
    hasVisibleOutsideTemplate (getter: bool)
    hasWidgetFactory (getter: bool)
  MethodElement (class extends Object implements ExecutableElement, HasSinceSdkVersion):
    CALL_METHOD_NAME (static getter: String)
    NO_SUCH_METHOD_METHOD_NAME (static getter: String)
    new (constructor: MethodElement Function())
    baseElement (getter: MethodElement)
    firstFragment (getter: MethodFragment)
    fragments (getter: List<MethodFragment>)
    isOperator (getter: bool)
  MethodFragment (class extends Object implements ExecutableFragment):
    new (constructor: MethodFragment Function())
    element (getter: MethodElement)
    enclosingFragment (getter: InstanceFragment?)
    nextFragment (getter: MethodFragment?)
    previousFragment (getter: MethodFragment?)
  MixinElement (class extends Object implements InterfaceElement):
    new (constructor: MixinElement Function())
    firstFragment (getter: MixinFragment)
    fragments (getter: List<MixinFragment>)
    isBase (getter: bool)
    superclassConstraints (getter: List<InterfaceType>)
    isImplementableIn2 (method: bool Function(LibraryElement))
  MixinFragment (class extends Object implements InterfaceFragment):
    new (constructor: MixinFragment Function())
    element (getter: MixinElement)
    nextFragment (getter: MixinFragment?)
    previousFragment (getter: MixinFragment?)
    superclassConstraints (getter: List<InterfaceType>)
  MultiplyDefinedElement (class extends Object implements Element):
    new (constructor: MultiplyDefinedElement Function())
    conflictingElements2 (getter: List<Element>)
    firstFragment (getter: MultiplyDefinedFragment)
    fragments (getter: List<MultiplyDefinedFragment>)
  MultiplyDefinedFragment (class extends Object implements Fragment):
    new (constructor: MultiplyDefinedFragment Function())
    element (getter: MultiplyDefinedElement)
    nextFragment (getter: Null)
    offset (getter: int)
    previousFragment (getter: Null)
  Name (class extends Object):
    forElement (static method: Name? Function(Element))
    forLibrary (constructor: Name Function(LibraryElement?, String))
    new (constructor: Name Function(Uri?, String))
    forGetter (getter: Name)
    forSetter (getter: Name)
    hashCode (getter: int)
    isPublic (getter: bool)
    libraryUri (getter: Uri?)
    name (getter: String)
    == (method: bool Function(Object))
    isAccessibleFor (method: bool Function(Uri))
    toString (method: String Function())
  NamespaceCombinator (class extends Object, sealed (immediate subtypes: HideElementCombinator, ShowElementCombinator)):
    end (getter: int)
    offset (getter: int)
  PartInclude (class extends Object implements ElementDirective):
    new (constructor: PartInclude Function())
    includedFragment (getter: LibraryFragment?)
  PatternVariableElement (class extends Object implements LocalVariableElement):
    new (constructor: PatternVariableElement Function())
    firstFragment (getter: PatternVariableFragment)
    fragments (getter: List<PatternVariableFragment>)
    join2 (getter: JoinPatternVariableElement?)
  PatternVariableFragment (class extends Object implements LocalVariableFragment):
    new (constructor: PatternVariableFragment Function())
    element (getter: PatternVariableElement)
    join2 (getter: JoinPatternVariableFragment?)
    nextFragment (getter: PatternVariableFragment?)
    previousFragment (getter: PatternVariableFragment?)
  PrefixElement (class extends Object implements Element):
    new (constructor: PrefixElement Function())
    enclosingElement (getter: Null)
    enclosingElement2 (getter: Null, deprecated)
    firstFragment (getter: PrefixFragment)
    fragments (getter: List<PrefixFragment>)
    imports (getter: List<LibraryImport>)
    library (getter: LibraryElement)
    library2 (getter: LibraryElement, deprecated)
    scope (getter: Scope)
  PrefixFragment (class extends Object implements Fragment):
    new (constructor: PrefixFragment Function())
    element (getter: PrefixElement)
    enclosingFragment (getter: LibraryFragment?)
    isDeferred (getter: bool)
    nextFragment (getter: PrefixFragment?)
    previousFragment (getter: PrefixFragment?)
  PromotableElement (class extends Object implements VariableElement):
    new (constructor: PromotableElement Function())
    firstFragment (getter: PromotableFragment)
    fragments (getter: List<PromotableFragment>)
  PromotableFragment (class extends Object implements VariableFragment):
    new (constructor: PromotableFragment Function())
    element (getter: PromotableElement)
    nextFragment (getter: PromotableFragment?)
    previousFragment (getter: PromotableFragment?)
  PropertyAccessorElement (class extends Object implements ExecutableElement):
    new (constructor: PropertyAccessorElement Function())
    baseElement (getter: PropertyAccessorElement)
    enclosingElement (getter: Element)
    enclosingElement2 (getter: Element, deprecated)
    firstFragment (getter: PropertyAccessorFragment)
    fragments (getter: List<PropertyAccessorFragment>)
    variable3 (getter: PropertyInducingElement?)
  PropertyAccessorFragment (class extends Object implements ExecutableFragment):
    new (constructor: PropertyAccessorFragment Function())
    element (getter: PropertyAccessorElement)
    nextFragment (getter: PropertyAccessorFragment?)
    previousFragment (getter: PropertyAccessorFragment?)
  PropertyInducingElement (class extends Object implements VariableElement, Annotatable, HasSinceSdkVersion):
    new (constructor: PropertyInducingElement Function())
    firstFragment (getter: PropertyInducingFragment)
    fragments (getter: List<PropertyInducingFragment>)
    getter2 (getter: GetterElement?)
    hasInitializer (getter: bool)
    library (getter: LibraryElement)
    library2 (getter: LibraryElement, deprecated)
    setter2 (getter: SetterElement?)
  PropertyInducingFragment (class extends Object implements VariableFragment, Annotatable):
    new (constructor: PropertyInducingFragment Function())
    element (getter: PropertyInducingElement)
    hasInitializer (getter: bool)
    isAugmentation (getter: bool)
    isSynthetic (getter: bool)
    libraryFragment (getter: LibraryFragment)
    nextFragment (getter: PropertyInducingFragment?)
    previousFragment (getter: PropertyInducingFragment?)
  SetterElement (class extends Object implements PropertyAccessorElement):
    new (constructor: SetterElement Function())
    baseElement (getter: SetterElement)
    correspondingGetter2 (getter: GetterElement?)
    firstFragment (getter: SetterFragment)
    fragments (getter: List<SetterFragment>)
  SetterFragment (class extends Object implements PropertyAccessorFragment):
    new (constructor: SetterFragment Function())
    element (getter: SetterElement)
    nextFragment (getter: SetterFragment?)
    offset (getter: int)
    previousFragment (getter: SetterFragment?)
  ShowElementCombinator (class extends Object implements NamespaceCombinator):
    new (constructor: ShowElementCombinator Function())
    shownNames (getter: List<String>)
  SuperFormalParameterElement (class extends Object implements FormalParameterElement):
    new (constructor: SuperFormalParameterElement Function())
    firstFragment (getter: SuperFormalParameterFragment)
    fragments (getter: List<SuperFormalParameterFragment>)
    superConstructorParameter2 (getter: FormalParameterElement?)
  SuperFormalParameterFragment (class extends Object implements FormalParameterFragment):
    new (constructor: SuperFormalParameterFragment Function())
    element (getter: SuperFormalParameterElement)
    nextFragment (getter: SuperFormalParameterFragment?)
    previousFragment (getter: SuperFormalParameterFragment?)
  TopLevelFunctionElement (class extends Object implements ExecutableElement, HasSinceSdkVersion):
    LOAD_LIBRARY_NAME (static getter: String)
    MAIN_FUNCTION_NAME (static getter: String)
    new (constructor: TopLevelFunctionElement Function())
    baseElement (getter: TopLevelFunctionElement)
    firstFragment (getter: TopLevelFunctionFragment)
    fragments (getter: List<TopLevelFunctionFragment>)
    isDartCoreIdentical (getter: bool)
    isEntryPoint (getter: bool)
  TopLevelFunctionFragment (class extends Object implements ExecutableFragment):
    new (constructor: TopLevelFunctionFragment Function())
    element (getter: TopLevelFunctionElement)
    nextFragment (getter: TopLevelFunctionFragment?)
    previousFragment (getter: TopLevelFunctionFragment?)
  TopLevelVariableElement (class extends Object implements PropertyInducingElement):
    new (constructor: TopLevelVariableElement Function())
    baseElement (getter: TopLevelVariableElement)
    firstFragment (getter: TopLevelVariableFragment)
    fragments (getter: List<TopLevelVariableFragment>)
    isExternal (getter: bool)
  TopLevelVariableFragment (class extends Object implements PropertyInducingFragment):
    new (constructor: TopLevelVariableFragment Function())
    element (getter: TopLevelVariableElement)
    nextFragment (getter: TopLevelVariableFragment?)
    previousFragment (getter: TopLevelVariableFragment?)
  TypeAliasElement (class extends Object implements TypeParameterizedElement, TypeDefiningElement, HasSinceSdkVersion):
    new (constructor: TypeAliasElement Function())
    aliasedElement2 (getter: Element?)
    aliasedType (getter: DartType)
    enclosingElement (getter: LibraryElement)
    enclosingElement2 (getter: LibraryElement, deprecated)
    firstFragment (getter: TypeAliasFragment)
    fragments (getter: List<TypeAliasFragment>)
    instantiate (method: DartType Function({required NullabilitySuffix nullabilitySuffix, required List<DartType> typeArguments}))
  TypeAliasFragment (class extends Object implements TypeParameterizedFragment, TypeDefiningFragment):
    new (constructor: TypeAliasFragment Function())
    element (getter: TypeAliasElement)
    enclosingFragment (getter: LibraryFragment?)
    nextFragment (getter: TypeAliasFragment?)
    previousFragment (getter: TypeAliasFragment?)
  TypeDefiningElement (class extends Object implements Element, Annotatable):
    new (constructor: TypeDefiningElement Function())
    firstFragment (getter: TypeDefiningFragment)
    fragments (getter: List<TypeDefiningFragment>)
  TypeDefiningFragment (class extends Object implements Fragment, Annotatable):
    new (constructor: TypeDefiningFragment Function())
    element (getter: TypeDefiningElement)
    nextFragment (getter: TypeDefiningFragment?)
    offset (getter: int)
    previousFragment (getter: TypeDefiningFragment?)
  TypeParameterElement (class extends Object implements TypeDefiningElement):
    new (constructor: TypeParameterElement Function())
    baseElement (getter: TypeParameterElement)
    bound (getter: DartType?)
    firstFragment (getter: TypeParameterFragment)
    fragments (getter: List<TypeParameterFragment>)
    instantiate (method: TypeParameterType Function({required NullabilitySuffix nullabilitySuffix}))
  TypeParameterFragment (class extends Object implements TypeDefiningFragment):
    new (constructor: TypeParameterFragment Function())
    element (getter: TypeParameterElement)
    nextFragment (getter: TypeParameterFragment?)
    previousFragment (getter: TypeParameterFragment?)
  TypeParameterizedElement (class extends Object implements Element, Annotatable):
    new (constructor: TypeParameterizedElement Function())
    firstFragment (getter: TypeParameterizedFragment)
    fragments (getter: List<TypeParameterizedFragment>)
    isSimplyBounded (getter: bool)
    library (getter: LibraryElement)
    library2 (getter: LibraryElement, deprecated)
    typeParameters2 (getter: List<TypeParameterElement>)
  TypeParameterizedFragment (class extends Object implements Fragment, Annotatable):
    new (constructor: TypeParameterizedFragment Function())
    element (getter: TypeParameterizedElement)
    nextFragment (getter: TypeParameterizedFragment?)
    previousFragment (getter: TypeParameterizedFragment?)
    typeParameters2 (getter: List<TypeParameterFragment>)
  VariableElement (class extends Object implements Element):
    new (constructor: VariableElement Function())
    constantInitializer2 (getter: ConstantInitializer?)
    firstFragment (getter: VariableFragment)
    fragments (getter: List<VariableFragment>)
    hasImplicitType (getter: bool)
    isConst (getter: bool)
    isFinal (getter: bool)
    isLate (getter: bool)
    isStatic (getter: bool)
    type (getter: DartType)
    computeConstantValue (method: DartObject? Function())
  VariableFragment (class extends Object implements Fragment):
    new (constructor: VariableFragment Function())
    element (getter: VariableElement)
    initializer (getter: Expression?)
    nextFragment (getter: VariableFragment?)
    previousFragment (getter: VariableFragment?)
  BindPatternVariableElement2 (type alias for BindPatternVariableElement, deprecated)
  ClassElement2 (type alias for ClassElement, deprecated)
  ConstructorElement2 (type alias for ConstructorElement, deprecated)
  Element2 (type alias for Element, deprecated)
  EnumElement2 (type alias for EnumElement, deprecated)
  ExecutableElement2 (type alias for ExecutableElement, deprecated)
  ExtensionElement2 (type alias for ExtensionElement, deprecated)
  ExtensionTypeElement2 (type alias for ExtensionTypeElement, deprecated)
  FieldElement2 (type alias for FieldElement, deprecated)
  FieldFormalParameterElement2 (type alias for FieldFormalParameterElement, deprecated)
  FunctionTypedElement2 (type alias for FunctionTypedElement, deprecated)
  GenericFunctionTypeElement2 (type alias for GenericFunctionTypeElement, deprecated)
  InstanceElement2 (type alias for InstanceElement, deprecated)
  InterfaceElement2 (type alias for InterfaceElement, deprecated)
  JoinPatternVariableElement2 (type alias for JoinPatternVariableElement, deprecated)
  LabelElement2 (type alias for LabelElement, deprecated)
  LibraryElement2 (type alias for LibraryElement, deprecated)
  LocalElement2 (type alias for LocalElement, deprecated)
  LocalVariableElement2 (type alias for LocalVariableElement, deprecated)
  MethodElement2 (type alias for MethodElement, deprecated)
  MixinElement2 (type alias for MixinElement, deprecated)
  MultiplyDefinedElement2 (type alias for MultiplyDefinedElement, deprecated)
  PatternVariableElement2 (type alias for PatternVariableElement, deprecated)
  PrefixElement2 (type alias for PrefixElement, deprecated)
  PromotableElement2 (type alias for PromotableElement, deprecated)
  PropertyAccessorElement2 (type alias for PropertyAccessorElement, deprecated)
  PropertyInducingElement2 (type alias for PropertyInducingElement, deprecated)
  SuperFormalParameterElement2 (type alias for SuperFormalParameterElement, deprecated)
  TopLevelVariableElement2 (type alias for TopLevelVariableElement, deprecated)
  TypeAliasElement2 (type alias for TypeAliasElement, deprecated)
  TypeDefiningElement2 (type alias for TypeDefiningElement, deprecated)
  TypeParameterElement2 (type alias for TypeParameterElement, deprecated)
  TypeParameterizedElement2 (type alias for TypeParameterizedElement, deprecated)
  VariableElement2 (type alias for VariableElement, deprecated)
package:analyzer/dart/element/element2.dart:
  Annotatable (see above)
  BindPatternVariableElement (see above)
  BindPatternVariableFragment (see above)
  ClassElement (see above)
  ClassFragment (see above)
  ConstantInitializer (see above)
  ConstructorElement (see above)
  ConstructorFragment (see above)
  DirectiveUri (see above)
  DirectiveUriWithLibrary (see above)
  DirectiveUriWithRelativeUri (see above)
  DirectiveUriWithRelativeUriString (see above)
  DirectiveUriWithSource (see above)
  DirectiveUriWithUnit (see above)
  Element (see above)
  ElementAnnotation (see above)
  ElementDirective (see above)
  ElementKind (see above)
  ElementVisitor2 (see above)
  EnumElement (see above)
  EnumFragment (see above)
  ExecutableElement (see above)
  ExecutableFragment (see above)
  ExtensionElement (see above)
  ExtensionFragment (see above)
  ExtensionTypeElement (see above)
  ExtensionTypeFragment (see above)
  FieldElement (see above)
  FieldFormalParameterElement (see above)
  FieldFormalParameterFragment (see above)
  FieldFragment (see above)
  FormalParameterElement (see above)
  FormalParameterFragment (see above)
  Fragment (see above)
  FunctionTypedElement (see above)
  FunctionTypedFragment (see above)
  GenericFunctionTypeElement (see above)
  GenericFunctionTypeFragment (see above)
  GetterElement (see above)
  GetterFragment (see above)
  HasSinceSdkVersion (see above)
  HideElementCombinator (see above)
  InstanceElement (see above)
  InstanceFragment (see above)
  InterfaceElement (see above)
  InterfaceFragment (see above)
  JoinPatternVariableElement (see above)
  JoinPatternVariableFragment (see above)
  LabelElement (see above)
  LabelFragment (see above)
  LibraryElement (see above)
  LibraryExport (see above)
  LibraryFragment (see above)
  LibraryImport (see above)
  LibraryLanguageVersion (see above)
  LocalElement (see above)
  LocalFragment (see above)
  LocalFunctionElement (see above)
  LocalFunctionFragment (see above)
  LocalVariableElement (see above)
  LocalVariableFragment (see above)
  Metadata (see above)
  MethodElement (see above)
  MethodFragment (see above)
  MixinElement (see above)
  MixinFragment (see above)
  MultiplyDefinedElement (see above)
  MultiplyDefinedFragment (see above)
  Name (see above)
  NamespaceCombinator (see above)
  PartInclude (see above)
  PatternVariableElement (see above)
  PatternVariableFragment (see above)
  PrefixElement (see above)
  PrefixFragment (see above)
  PromotableElement (see above)
  PromotableFragment (see above)
  PropertyAccessorElement (see above)
  PropertyAccessorFragment (see above)
  PropertyInducingElement (see above)
  PropertyInducingFragment (see above)
  SetterElement (see above)
  SetterFragment (see above)
  ShowElementCombinator (see above)
  SuperFormalParameterElement (see above)
  SuperFormalParameterFragment (see above)
  TopLevelFunctionElement (see above)
  TopLevelFunctionFragment (see above)
  TopLevelVariableElement (see above)
  TopLevelVariableFragment (see above)
  TypeAliasElement (see above)
  TypeAliasFragment (see above)
  TypeDefiningElement (see above)
  TypeDefiningFragment (see above)
  TypeParameterElement (see above)
  TypeParameterFragment (see above)
  TypeParameterizedElement (see above)
  TypeParameterizedFragment (see above)
  VariableElement (see above)
  VariableFragment (see above)
  BindPatternVariableElement2 (see above)
  ClassElement2 (see above)
  ConstructorElement2 (see above)
  Element2 (see above)
  EnumElement2 (see above)
  ExecutableElement2 (see above)
  ExtensionElement2 (see above)
  ExtensionTypeElement2 (see above)
  FieldElement2 (see above)
  FieldFormalParameterElement2 (see above)
  FunctionTypedElement2 (see above)
  GenericFunctionTypeElement2 (see above)
  InstanceElement2 (see above)
  InterfaceElement2 (see above)
  JoinPatternVariableElement2 (see above)
  LabelElement2 (see above)
  LibraryElement2 (see above)
  LocalElement2 (see above)
  LocalVariableElement2 (see above)
  MethodElement2 (see above)
  MixinElement2 (see above)
  MultiplyDefinedElement2 (see above)
  PatternVariableElement2 (see above)
  PrefixElement2 (see above)
  PromotableElement2 (see above)
  PropertyAccessorElement2 (see above)
  PropertyInducingElement2 (see above)
  SuperFormalParameterElement2 (see above)
  TopLevelVariableElement2 (see above)
  TypeAliasElement2 (see above)
  TypeDefiningElement2 (see above)
  TypeParameterElement2 (see above)
  TypeParameterizedElement2 (see above)
  VariableElement2 (see above)
package:analyzer/dart/element/nullability_suffix.dart:
  NullabilitySuffix (enum):
    none (static getter: NullabilitySuffix)
    question (static getter: NullabilitySuffix)
    star (static getter: NullabilitySuffix)
    values (static getter: List<NullabilitySuffix>)
package:analyzer/dart/element/scope.dart:
  Scope (class extends Object):
    new (constructor: Scope Function())
    lookup (method: ScopeLookupResult Function(String))
  ScopeLookupResult (class extends Object):
    new (constructor: ScopeLookupResult Function())
    getter2 (getter: Element?)
    setter2 (getter: Element?)
package:analyzer/dart/element/type.dart:
  DartType (class extends Object):
    new (constructor: DartType Function())
    alias (getter: InstantiatedTypeAliasElement?)
    element (getter: Element?, experimental)
    element3 (getter: Element?, deprecated, experimental)
    extensionTypeErasure (getter: DartType)
    isBottom (getter: bool)
    isDartAsyncFuture (getter: bool)
    isDartAsyncFutureOr (getter: bool)
    isDartAsyncStream (getter: bool)
    isDartCoreBool (getter: bool)
    isDartCoreDouble (getter: bool)
    isDartCoreEnum (getter: bool)
    isDartCoreFunction (getter: bool)
    isDartCoreInt (getter: bool)
    isDartCoreIterable (getter: bool)
    isDartCoreList (getter: bool)
    isDartCoreMap (getter: bool)
    isDartCoreNull (getter: bool)
    isDartCoreNum (getter: bool)
    isDartCoreObject (getter: bool)
    isDartCoreRecord (getter: bool)
    isDartCoreSet (getter: bool)
    isDartCoreString (getter: bool)
    isDartCoreSymbol (getter: bool)
    isDartCoreType (getter: bool)
    name (getter: String?, deprecated)
    nullabilitySuffix (getter: NullabilitySuffix)
    accept (method: R Function<R>(TypeVisitor<R>))
    acceptWithArgument (method: R Function<R, A>(TypeVisitorWithArgument<R, A>, A))
    asInstanceOf2 (method: InterfaceType? Function(InterfaceElement), experimental)
    getDisplayString (method: String Function({bool withNullability}))
  DynamicType (class extends Object implements DartType):
    new (constructor: DynamicType Function())
  FunctionType (class extends Object implements DartType):
    new (constructor: FunctionType Function())
    element (getter: Null)
    element3 (getter: Null, deprecated)
    formalParameters (getter: List<FormalParameterElement>, experimental)
    namedParameterTypes (getter: Map<String, DartType>)
    normalParameterTypes (getter: List<DartType>)
    optionalParameterTypes (getter: List<DartType>)
    returnType (getter: DartType)
    typeParameters (getter: List<TypeParameterElement>, experimental)
    instantiate (method: FunctionType Function(List<DartType>))
  InstantiatedTypeAliasElement (class extends Object):
    new (constructor: InstantiatedTypeAliasElement Function())
    element (getter: TypeAliasElement, experimental)
    element2 (getter: TypeAliasElement, deprecated, experimental)
    typeArguments (getter: List<DartType>)
  InterfaceType (class extends Object implements ParameterizedType):
    new (constructor: InterfaceType Function())
    allSupertypes (getter: List<InterfaceType>)
    constructors (getter: List<ConstructorElement>, experimental)
    constructors2 (getter: List<ConstructorElement>, deprecated, experimental)
    element (getter: InterfaceElement, experimental)
    element3 (getter: InterfaceElement, deprecated, experimental)
    getters (getter: List<GetterElement>, experimental)
    interfaces (getter: List<InterfaceType>)
    methods2 (getter: List<MethodElement>, experimental)
    mixins (getter: List<InterfaceType>)
    setters (getter: List<SetterElement>, experimental)
    superclass (getter: InterfaceType?)
    superclassConstraints (getter: List<InterfaceType>)
    getGetter (method: GetterElement? Function(String))
    getGetter2 (method: GetterElement? Function(String), deprecated)
    getMethod (method: MethodElement? Function(String))
    getMethod2 (method: MethodElement? Function(String), deprecated)
    getSetter (method: SetterElement? Function(String))
    getSetter2 (method: SetterElement? Function(String), deprecated)
    lookUpConstructor (method: ConstructorElement? Function(String?, LibraryElement))
    lookUpConstructor2 (method: ConstructorElement? Function(String?, LibraryElement), deprecated)
    lookUpGetter (method: GetterElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}))
    lookUpGetter3 (method: GetterElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}), deprecated)
    lookUpMethod (method: MethodElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}))
    lookUpMethod3 (method: MethodElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}), deprecated)
    lookUpSetter (method: SetterElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}))
    lookUpSetter3 (method: SetterElement? Function(String, LibraryElement, {bool concrete, bool inherited, bool recoveryStatic}), deprecated)
  InvalidType (class extends Object implements DartType):
    new (constructor: InvalidType Function())
  NeverType (class extends Object implements DartType):
    new (constructor: NeverType Function())
  ParameterizedType (class extends Object implements DartType):
    new (constructor: ParameterizedType Function())
    typeArguments (getter: List<DartType>)
  RecordType (class extends Object implements DartType):
    new (constructor: RecordType Function({required Map<String, DartType> named, required NullabilitySuffix nullabilitySuffix, required List<DartType> positional}))
    element (getter: Null)
    element3 (getter: Null, deprecated)
    namedFields (getter: List<RecordTypeNamedField>)
    positionalFields (getter: List<RecordTypePositionalField>)
  RecordTypeField (class extends Object):
    new (constructor: RecordTypeField Function())
    type (getter: DartType)
  RecordTypeNamedField (class extends Object implements RecordTypeField):
    new (constructor: RecordTypeNamedField Function())
    name (getter: String)
  RecordTypePositionalField (class extends Object implements RecordTypeField):
    new (constructor: RecordTypePositionalField Function())
  TypeParameterType (class extends Object implements DartType):
    new (constructor: TypeParameterType Function())
    bound (getter: DartType)
    element (getter: TypeParameterElement, experimental)
    element3 (getter: TypeParameterElement, deprecated, experimental)
  VoidType (class extends Object implements DartType):
    new (constructor: VoidType Function())
    element (getter: Null)
    element3 (getter: Null, deprecated)
package:analyzer/dart/element/type_provider.dart:
  TypeProvider (class extends Object):
    new (constructor: TypeProvider Function())
    boolElement (getter: ClassElement, experimental)
    boolElement2 (getter: ClassElement, deprecated, experimental)
    boolType (getter: InterfaceType)
    bottomType (getter: DartType)
    deprecatedType (getter: InterfaceType)
    doubleElement (getter: ClassElement, experimental)
    doubleElement2 (getter: ClassElement, deprecated, experimental)
    doubleType (getter: InterfaceType)
    dynamicType (getter: DartType)
    enumElement (getter: ClassElement?, experimental)
    enumElement2 (getter: ClassElement?, deprecated, experimental)
    enumType (getter: InterfaceType?)
    functionType (getter: InterfaceType)
    futureDynamicType (getter: InterfaceType)
    futureElement (getter: ClassElement, experimental)
    futureElement2 (getter: ClassElement, deprecated, experimental)
    futureNullType (getter: InterfaceType)
    futureOrElement (getter: ClassElement, experimental)
    futureOrElement2 (getter: ClassElement, deprecated, experimental)
    futureOrNullType (getter: InterfaceType)
    intElement (getter: ClassElement, experimental)
    intElement2 (getter: ClassElement, deprecated, experimental)
    intType (getter: InterfaceType)
    iterableDynamicType (getter: InterfaceType)
    iterableElement (getter: ClassElement, experimental)
    iterableElement2 (getter: ClassElement, deprecated, experimental)
    iterableObjectType (getter: InterfaceType)
    listElement (getter: ClassElement, experimental)
    listElement2 (getter: ClassElement, deprecated, experimental)
    mapElement (getter: ClassElement, experimental)
    mapElement2 (getter: ClassElement, deprecated, experimental)
    mapObjectObjectType (getter: InterfaceType)
    neverType (getter: NeverType)
    nullElement (getter: ClassElement, experimental)
    nullElement2 (getter: ClassElement, deprecated, experimental)
    nullType (getter: InterfaceType)
    numElement (getter: ClassElement, experimental)
    numElement2 (getter: ClassElement, deprecated, experimental)
    numType (getter: InterfaceType)
    objectElement (getter: ClassElement, experimental)
    objectElement2 (getter: ClassElement, deprecated, experimental)
    objectQuestionType (getter: InterfaceType)
    objectType (getter: InterfaceType)
    recordElement (getter: ClassElement, experimental)
    recordElement2 (getter: ClassElement, deprecated, experimental)
    recordType (getter: InterfaceType)
    setElement (getter: ClassElement, experimental)
    setElement2 (getter: ClassElement, deprecated, experimental)
    stackTraceType (getter: InterfaceType)
    streamDynamicType (getter: InterfaceType)
    streamElement (getter: ClassElement, experimental)
    streamElement2 (getter: ClassElement, deprecated, experimental)
    stringElement (getter: ClassElement, experimental)
    stringElement2 (getter: ClassElement, deprecated, experimental)
    stringType (getter: InterfaceType)
    symbolElement (getter: ClassElement, experimental)
    symbolElement2 (getter: ClassElement, deprecated, experimental)
    symbolType (getter: InterfaceType)
    typeType (getter: InterfaceType)
    voidType (getter: VoidType)
    futureOrType (method: InterfaceType Function(DartType))
    futureType (method: InterfaceType Function(DartType))
    isNonSubtypableClass2 (method: bool Function(InterfaceElement))
    isObjectGetter (method: bool Function(String))
    isObjectMember (method: bool Function(String))
    isObjectMethod (method: bool Function(String))
    iterableType (method: InterfaceType Function(DartType))
    listType (method: InterfaceType Function(DartType))
    mapType (method: InterfaceType Function(DartType, DartType))
    setType (method: InterfaceType Function(DartType))
    streamType (method: InterfaceType Function(DartType))
package:analyzer/dart/element/type_system.dart:
  TypeSystem (class extends Object):
    new (constructor: TypeSystem Function())
    flatten (method: DartType Function(DartType))
    greatestLowerBound (method: DartType Function(DartType, DartType))
    instantiateInterfaceToBounds2 (method: InterfaceType Function({required InterfaceElement element, required NullabilitySuffix nullabilitySuffix}), experimental)
    instantiateTypeAliasToBounds2 (method: DartType Function({required TypeAliasElement element, required NullabilitySuffix nullabilitySuffix}), experimental)
    isAssignableTo (method: bool Function(DartType, DartType, {bool strictCasts}))
    isNonNullable (method: bool Function(DartType))
    isNullable (method: bool Function(DartType))
    isPotentiallyNonNullable (method: bool Function(DartType))
    isPotentiallyNullable (method: bool Function(DartType))
    isStrictlyNonNullable (method: bool Function(DartType))
    isSubtypeOf (method: bool Function(DartType, DartType))
    leastUpperBound (method: DartType Function(DartType, DartType))
    promoteToNonNull (method: DartType Function(DartType))
    resolveToBound (method: DartType Function(DartType))
package:analyzer/dart/element/type_visitor.dart:
  TypeVisitor (class<R> extends Object):
    new (constructor: TypeVisitor<R> Function())
    visitDynamicType (method: R Function(DynamicType))
    visitFunctionType (method: R Function(FunctionType))
    visitInterfaceType (method: R Function(InterfaceType))
    visitInvalidType (method: R Function(InvalidType))
    visitNeverType (method: R Function(NeverType))
    visitRecordType (method: R Function(RecordType))
    visitTypeParameterType (method: R Function(TypeParameterType))
    visitVoidType (method: R Function(VoidType))
  TypeVisitorWithArgument (class<R, A> extends Object):
    new (constructor: TypeVisitorWithArgument<R, A> Function())
    visitDynamicType (method: R Function(DynamicType, A))
    visitFunctionType (method: R Function(FunctionType, A))
    visitInterfaceType (method: R Function(InterfaceType, A))
    visitInvalidType (method: R Function(InvalidType, A))
    visitNeverType (method: R Function(NeverType, A))
    visitRecordType (method: R Function(RecordType, A))
    visitTypeParameterType (method: R Function(TypeParameterType, A))
    visitVoidType (method: R Function(VoidType, A))
  UnifyingTypeVisitor (class<R> extends Object implements TypeVisitor<R>):
    new (constructor: UnifyingTypeVisitor<R> Function())
    visitDartType (method: R Function(DartType))
    visitDynamicType (method: R Function(DynamicType))
    visitFunctionType (method: R Function(FunctionType))
    visitInterfaceType (method: R Function(InterfaceType))
    visitInvalidType (method: R Function(InvalidType))
    visitNeverType (method: R Function(NeverType))
    visitRecordType (method: R Function(RecordType))
    visitTypeParameterType (method: R Function(TypeParameterType))
    visitVoidType (method: R Function(VoidType))
  UnifyingTypeVisitorWithArgument (class<R, A> extends Object implements TypeVisitorWithArgument<R, A>):
    new (constructor: UnifyingTypeVisitorWithArgument<R, A> Function())
    visitDartType (method: R Function(DartType, A))
    visitDynamicType (method: R Function(DynamicType, A))
    visitFunctionType (method: R Function(FunctionType, A))
    visitInterfaceType (method: R Function(InterfaceType, A))
    visitInvalidType (method: R Function(InvalidType, A))
    visitNeverType (method: R Function(NeverType, A))
    visitRecordType (method: R Function(RecordType, A))
    visitTypeParameterType (method: R Function(TypeParameterType, A))
    visitVoidType (method: R Function(VoidType, A))
package:analyzer/dart/element/visitor2.dart:
  GeneralizingElementVisitor2 (class<R> extends Object implements ElementVisitor2<R>):
    new (constructor: GeneralizingElementVisitor2<R> Function())
    visitClassElement (method: R? Function(ClassElement))
    visitConstructorElement (method: R? Function(ConstructorElement))
    visitElement (method: R? Function(Element))
    visitEnumElement (method: R? Function(EnumElement))
    visitExecutableElement (method: R? Function(ExecutableElement))
    visitExtensionElement (method: R? Function(ExtensionElement))
    visitExtensionTypeElement (method: R? Function(ExtensionTypeElement))
    visitFieldElement (method: R? Function(FieldElement))
    visitFieldFormalParameterElement (method: R? Function(FieldFormalParameterElement))
    visitFormalParameterElement (method: R? Function(FormalParameterElement))
    visitGenericFunctionTypeElement (method: R? Function(GenericFunctionTypeElement))
    visitGetterElement (method: R? Function(GetterElement))
    visitLabelElement (method: R? Function(LabelElement))
    visitLibraryElement (method: R? Function(LibraryElement))
    visitLocalFunctionElement (method: R? Function(LocalFunctionElement))
    visitLocalVariableElement (method: R? Function(LocalVariableElement))
    visitMethodElement (method: R? Function(MethodElement))
    visitMixinElement (method: R? Function(MixinElement))
    visitMultiplyDefinedElement (method: R? Function(MultiplyDefinedElement))
    visitPrefixElement (method: R? Function(PrefixElement))
    visitPropertyAccessorElement (method: R? Function(PropertyAccessorElement))
    visitPropertyInducingElement (method: R? Function(PropertyInducingElement))
    visitSetterElement (method: R? Function(SetterElement))
    visitSuperFormalParameterElement (method: R? Function(SuperFormalParameterElement))
    visitTopLevelFunctionElement (method: R? Function(TopLevelFunctionElement))
    visitTopLevelVariableElement (method: R? Function(TopLevelVariableElement))
    visitTypeAliasElement (method: R? Function(TypeAliasElement))
    visitTypeParameterElement (method: R? Function(TypeParameterElement))
    visitVariableElement (method: R? Function(VariableElement))
  RecursiveElementVisitor2 (class<R> extends Object implements ElementVisitor2<R>):
    new (constructor: RecursiveElementVisitor2<R> Function())
    visitClassElement (method: R? Function(ClassElement))
    visitConstructorElement (method: R? Function(ConstructorElement))
    visitEnumElement (method: R? Function(EnumElement))
    visitExtensionElement (method: R? Function(ExtensionElement))
    visitExtensionTypeElement (method: R? Function(ExtensionTypeElement))
    visitFieldElement (method: R? Function(FieldElement))
    visitFieldFormalParameterElement (method: R? Function(FieldFormalParameterElement))
    visitFormalParameterElement (method: R? Function(FormalParameterElement))
    visitGenericFunctionTypeElement (method: R? Function(GenericFunctionTypeElement))
    visitGetterElement (method: R? Function(GetterElement))
    visitLabelElement (method: R? Function(LabelElement))
    visitLibraryElement (method: R? Function(LibraryElement))
    visitLocalFunctionElement (method: R? Function(LocalFunctionElement))
    visitLocalVariableElement (method: R? Function(LocalVariableElement))
    visitMethodElement (method: R? Function(MethodElement))
    visitMixinElement (method: R? Function(MixinElement))
    visitMultiplyDefinedElement (method: R? Function(MultiplyDefinedElement))
    visitPrefixElement (method: R? Function(PrefixElement))
    visitSetterElement (method: R? Function(SetterElement))
    visitSuperFormalParameterElement (method: R? Function(SuperFormalParameterElement))
    visitTopLevelFunctionElement (method: R? Function(TopLevelFunctionElement))
    visitTopLevelVariableElement (method: R? Function(TopLevelVariableElement))
    visitTypeAliasElement (method: R? Function(TypeAliasElement))
    visitTypeParameterElement (method: R? Function(TypeParameterElement))
  SimpleElementVisitor2 (class<R> extends Object implements ElementVisitor2<R>):
    new (constructor: SimpleElementVisitor2<R> Function())
    visitClassElement (method: R? Function(ClassElement))
    visitConstructorElement (method: R? Function(ConstructorElement))
    visitEnumElement (method: R? Function(EnumElement))
    visitExtensionElement (method: R? Function(ExtensionElement))
    visitExtensionTypeElement (method: R? Function(ExtensionTypeElement))
    visitFieldElement (method: R? Function(FieldElement))
    visitFieldFormalParameterElement (method: R? Function(FieldFormalParameterElement))
    visitFormalParameterElement (method: R? Function(FormalParameterElement))
    visitGenericFunctionTypeElement (method: R? Function(GenericFunctionTypeElement))
    visitGetterElement (method: R? Function(GetterElement))
    visitLabelElement (method: R? Function(LabelElement))
    visitLibraryElement (method: R? Function(LibraryElement))
    visitLocalFunctionElement (method: R? Function(LocalFunctionElement))
    visitLocalVariableElement (method: R? Function(LocalVariableElement))
    visitMethodElement (method: R? Function(MethodElement))
    visitMixinElement (method: R? Function(MixinElement))
    visitMultiplyDefinedElement (method: R? Function(MultiplyDefinedElement))
    visitPrefixElement (method: R? Function(PrefixElement))
    visitSetterElement (method: R? Function(SetterElement))
    visitSuperFormalParameterElement (method: R? Function(SuperFormalParameterElement))
    visitTopLevelFunctionElement (method: R? Function(TopLevelFunctionElement))
    visitTopLevelVariableElement (method: R? Function(TopLevelVariableElement))
    visitTypeAliasElement (method: R? Function(TypeAliasElement))
    visitTypeParameterElement (method: R? Function(TypeParameterElement))
  ThrowingElementVisitor2 (class<R> extends Object implements ElementVisitor2<R>):
    new (constructor: ThrowingElementVisitor2<R> Function())
    visitClassElement (method: R? Function(ClassElement))
    visitConstructorElement (method: R? Function(ConstructorElement))
    visitEnumElement (method: R? Function(EnumElement))
    visitExtensionElement (method: R? Function(ExtensionElement))
    visitExtensionTypeElement (method: R? Function(ExtensionTypeElement))
    visitFieldElement (method: R? Function(FieldElement))
    visitFieldFormalParameterElement (method: R? Function(FieldFormalParameterElement))
    visitFormalParameterElement (method: R? Function(FormalParameterElement))
    visitGenericFunctionTypeElement (method: R? Function(GenericFunctionTypeElement))
    visitGetterElement (method: R? Function(GetterElement))
    visitLabelElement (method: R? Function(LabelElement))
    visitLibraryElement (method: R? Function(LibraryElement))
    visitLocalFunctionElement (method: R? Function(LocalFunctionElement))
    visitLocalVariableElement (method: R? Function(LocalVariableElement))
    visitMethodElement (method: R? Function(MethodElement))
    visitMixinElement (method: R? Function(MixinElement))
    visitMultiplyDefinedElement (method: R? Function(MultiplyDefinedElement))
    visitPrefixElement (method: R? Function(PrefixElement))
    visitSetterElement (method: R? Function(SetterElement))
    visitSuperFormalParameterElement (method: R? Function(SuperFormalParameterElement))
    visitTopLevelFunctionElement (method: R? Function(TopLevelFunctionElement))
    visitTopLevelVariableElement (method: R? Function(TopLevelVariableElement))
    visitTypeAliasElement (method: R? Function(TypeAliasElement))
    visitTypeParameterElement (method: R? Function(TypeParameterElement))
package:analyzer/dart/sdk/build_sdk_summary.dart:
  buildSdkSummary (function: Future<Uint8List> Function({String? embedderYamlPath, required ResourceProvider resourceProvider, required String sdkPath}))
package:analyzer/diagnostic/diagnostic.dart:
  Diagnostic (class extends Object):
    forValues (constructor: Diagnostic Function({List<DiagnosticMessage> contextMessages, String? correctionMessage, Object? data, DiagnosticCode? diagnosticCode, DiagnosticCode? errorCode, required int length, required String message, required int offset, required Source source}))
    tmp (constructor: Diagnostic Function({List<Object?> arguments, List<DiagnosticMessage> contextMessages, Object? data, DiagnosticCode? diagnosticCode, DiagnosticCode? errorCode, required int length, required int offset, required Source source}))
    contextMessages (getter: List<DiagnosticMessage>)
    correction (getter: String?, deprecated)
    correctionMessage (getter: String?)
    data (getter: Object?)
    diagnosticCode (getter: DiagnosticCode)
    errorCode (getter: DiagnosticCode, deprecated)
    hashCode (getter: int)
    length (getter: int)
    message (getter: String)
    offset (getter: int)
    problemMessage (getter: DiagnosticMessage)
    severity (getter: Severity)
    source (getter: Source)
    == (method: bool Function(Object))
    toString (method: String Function())
  DiagnosticMessage (class extends Object):
    new (constructor: DiagnosticMessage Function())
    filePath (getter: String)
    length (getter: int)
    offset (getter: int)
    url (getter: String?)
    messageText (method: String Function({required bool includeUrl}))
  Severity (enum):
    error (static getter: Severity)
    info (static getter: Severity)
    values (static getter: List<Severity>)
    warning (static getter: Severity)
package:analyzer/error/error.dart:
  diagnosticCodeValues (static getter: List<DiagnosticCode>)
  errorCodeValues (static getter: List<DiagnosticCode>, deprecated)
  errorCodeByUniqueName (function: DiagnosticCode? Function(String))
  DiagnosticCode (class extends Object):
    new (constructor: DiagnosticCode Function({String? correctionMessage, bool hasPublishedDocs, bool isUnresolvedIdentifier, required String name, required String problemMessage, required String uniqueName}))
    correctionMessage (getter: String?)
    errorSeverity (getter: DiagnosticSeverity, deprecated)
    hasPublishedDocs (getter: bool)
    isIgnorable (getter: bool)
    isUnresolvedIdentifier (getter: bool)
    name (getter: String)
    numParameters (getter: int)
    problemMessage (getter: String)
    severity (getter: DiagnosticSeverity)
    type (getter: DiagnosticType)
    uniqueName (getter: String)
    url (getter: String?)
    toString (method: String Function())
  DiagnosticSeverity (class extends Object implements Comparable<DiagnosticSeverity>):
    ERROR (static getter: DiagnosticSeverity)
    INFO (static getter: DiagnosticSeverity)
    NONE (static getter: DiagnosticSeverity)
    WARNING (static getter: DiagnosticSeverity)
    values (static getter: List<DiagnosticSeverity>)
    new (constructor: DiagnosticSeverity Function(String, int, String, String))
    displayName (getter: String)
    hashCode (getter: int)
    machineCode (getter: String)
    name (getter: String)
    ordinal (getter: int)
    compareTo (method: int Function(DiagnosticSeverity))
    max (method: DiagnosticSeverity Function(DiagnosticSeverity))
    toString (method: String Function())
  DiagnosticType (class extends Object implements Comparable<DiagnosticType>):
    CHECKED_MODE_COMPILE_TIME_ERROR (static getter: DiagnosticType)
    COMPILE_TIME_ERROR (static getter: DiagnosticType)
    HINT (static getter: DiagnosticType)
    LINT (static getter: DiagnosticType)
    STATIC_WARNING (static getter: DiagnosticType)
    SYNTACTIC_ERROR (static getter: DiagnosticType)
    TODO (static getter: DiagnosticType)
    values (static getter: List<DiagnosticType>)
    new (constructor: DiagnosticType Function(String, int, DiagnosticSeverity))
    displayName (getter: String)
    hashCode (getter: int)
    name (getter: String)
    ordinal (getter: int)
    severity (getter: DiagnosticSeverity)
    compareTo (method: int Function(DiagnosticType))
    toString (method: String Function())
  LintCode (class extends DiagnosticCode):
    new (constructor: LintCode Function(String, String, {String? correctionMessage, bool hasPublishedDocs, DiagnosticSeverity severity, String? uniqueName}))
    hashCode (getter: int)
    severity (getter: DiagnosticSeverity)
    type (getter: DiagnosticType)
    url (getter: String?)
    == (method: bool Function(Object))
  AnalysisError (type alias for Diagnostic, deprecated)
  ErrorCode (type alias for DiagnosticCode, deprecated)
  ErrorSeverity (type alias for DiagnosticSeverity, deprecated)
  ErrorType (type alias for DiagnosticType, deprecated)
package:analyzer/error/listener.dart:
  AnalysisErrorListener (class extends Object implements DiagnosticOrErrorListener, deprecated):
    NULL_LISTENER (static getter: AnalysisErrorListener)
    new (constructor: AnalysisErrorListener Function())
    onError (method: void Function(Diagnostic))
  BooleanDiagnosticListener (class extends Object implements AnalysisErrorListener, DiagnosticListener):
    new (constructor: BooleanDiagnosticListener Function())
    errorReported (getter: bool)
    onDiagnostic (method: void Function(Diagnostic))
    onError (method: void Function(Diagnostic))
  DiagnosticListener (class extends Object implements DiagnosticOrErrorListener):
    nullListener (static getter: DiagnosticListener)
    new (constructor: DiagnosticListener Function())
    onDiagnostic (method: void Function(Diagnostic))
  DiagnosticOrErrorListener (class extends Object, sealed (immediate subtypes: AnalysisErrorListener, DiagnosticListener))
  DiagnosticReporter (class extends Object):
    new (constructor: DiagnosticReporter Function(DiagnosticOrErrorListener, Source))
    lockLevel (getter: int)
    lockLevel= (setter: int)
    source (getter: Source)
    atConstructorDeclaration (method: void Function(ConstructorDeclaration, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}))
    atElement2 (method: void Function(Element, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}), experimental)
    atEntity (method: void Function(SyntacticEntity, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}))
    atNode (method: void Function(AstNode, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}))
    atOffset (method: void Function({List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data, DiagnosticCode? diagnosticCode, DiagnosticCode? errorCode, required int length, required int offset}))
    atSourceSpan (method: void Function(SourceSpan, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}))
    atToken (method: void Function(Token, DiagnosticCode, {List<Object>? arguments, List<DiagnosticMessage>? contextMessages, Object? data}))
    reportError (method: void Function(Diagnostic))
  RecordingDiagnosticListener (class extends Object implements AnalysisErrorListener, DiagnosticListener):
    new (constructor: RecordingDiagnosticListener Function())
    diagnostics (getter: List<Diagnostic>)
    errors (getter: List<Diagnostic>, deprecated)
    getErrorsForSource (method: List<Diagnostic> Function(Source), deprecated)
    onDiagnostic (method: void Function(Diagnostic))
    onError (method: void Function(Diagnostic))
  DiagnosticOrErrorListenerExtension (extension on DiagnosticOrErrorListener):
    onDiagnostic (method: void Function(Diagnostic))
  BooleanErrorListener (type alias for BooleanDiagnosticListener, deprecated)
  ErrorReporter (type alias for DiagnosticReporter, deprecated)
  RecorderingErrorListener (type alias for RecordingDiagnosticListener, deprecated)
package:analyzer/exception/exception.dart:
  AnalysisException (class extends Object implements Exception):
    new (constructor: AnalysisException Function([String, CaughtException?]))
    cause (getter: CaughtException?)
    message (getter: String)
    toString (method: String Function())
  CaughtException (class extends Object implements Exception):
    new (constructor: CaughtException Function(Object, StackTrace))
    withMessage (constructor: CaughtException Function(String?, Object, StackTrace))
    exception (getter: Object)
    message (getter: String?)
    rootCaughtException (getter: CaughtException)
    stackTrace (getter: StackTrace)
    stackTrace= (setter: StackTrace)
    toString (method: String Function())
  SilentException (class extends CaughtException):
    new (constructor: SilentException Function(String, Object, StackTrace))
    wrapInMessage (constructor: SilentException Function(String, CaughtException))
package:analyzer/file_system/file_system.dart:
  File (class extends Object implements Resource):
    new (constructor: File Function())
    lengthSync (getter: int)
    modificationStamp (getter: int)
    copyTo (method: File Function(Folder))
    readAsBytesSync (method: Uint8List Function())
    readAsStringSync (method: String Function())
    renameSync (method: File Function(String))
    watch (method: ResourceWatcher Function())
    writeAsBytesSync (method: void Function(List<int>))
    writeAsStringSync (method: void Function(String))
  FileSystemException (class extends Object implements Exception):
    new (constructor: FileSystemException Function(String, String))
    message (getter: String)
    path (getter: String)
    toString (method: String Function())
  Folder (class extends Object implements Resource):
    new (constructor: Folder Function())
    isRoot (getter: bool)
    canonicalizePath (method: String Function(String))
    contains (method: bool Function(String))
    copyTo (method: Folder Function(Folder))
    create (method: void Function())
    getChild (method: Resource Function(String))
    getChildAssumingFile (method: File Function(String))
    getChildAssumingFolder (method: Folder Function(String))
    getChildren (method: List<Resource> Function())
    watch (method: ResourceWatcher Function())
  Link (class extends Object):
    new (constructor: Link Function())
    exists (getter: bool)
    create (method: void Function(String))
  PathNotFoundException (class extends FileSystemException):
    new (constructor: PathNotFoundException Function(String, String))
    toString (method: String Function())
  Resource (class extends Object):
    new (constructor: Resource Function())
    exists (getter: bool)
    parent (getter: Folder)
    path (getter: String)
    provider (getter: ResourceProvider)
    shortName (getter: String)
    copyTo (method: Resource Function(Folder))
    delete (method: void Function())
    isOrContains (method: bool Function(String))
    resolveSymbolicLinksSync (method: Resource Function())
    toUri (method: Uri Function())
    watch (method: ResourceWatcher Function())
  ResourceProvider (class extends Object):
    new (constructor: ResourceProvider Function())
    pathContext (getter: Context)
    getFile (method: File Function(String))
    getFolder (method: Folder Function(String))
    getLink (method: Link Function(String))
    getResource (method: Resource Function(String))
    getStateLocation (method: Folder? Function(String))
  ResourceWatcher (class extends Object):
    new (constructor: ResourceWatcher Function(Stream<WatchEvent>, Future<void> Function()))
    changes (getter: Stream<WatchEvent>)
    ready (getter: Future<void>)
  FolderExtension (extension on Folder):
    withAncestors (getter: Iterable<Folder>)
    relativeIfContains (method: String? Function(String))
package:analyzer/file_system/memory_file_system.dart:
  MemoryResourceProvider (class extends Object implements ResourceProvider):
    new (constructor: MemoryResourceProvider Function({Context? context, Duration? delayWatcherInitialization}))
    delayWatcherInitialization (getter: Duration?)
    emitPathNotFoundExceptionsForPaths (getter: Set<String>)
    nextStamp (getter: int)
    nextStamp= (setter: int)
    pathContext (getter: Context)
    convertPath (method: String Function(String), deprecated)
    deleteFile (method: void Function(String))
    deleteFolder (method: void Function(String))
    getFile (method: File Function(String))
    getFolder (method: Folder Function(String))
    getLink (method: Link Function(String))
    getResource (method: Resource Function(String))
    getStateLocation (method: Folder Function(String))
    modifyFile (method: void Function(String, String))
    newFile (method: File Function(String, String))
    newFileWithBytes (method: File Function(String, List<int>))
    newFolder (method: Folder Function(String))
    newLink (method: void Function(String, String))
    writeOn (method: void Function(StringSink))
package:analyzer/file_system/overlay_file_system.dart:
  OverlayResourceProvider (class extends Object implements ResourceProvider):
    new (constructor: OverlayResourceProvider Function(ResourceProvider))
    baseProvider (getter: ResourceProvider)
    pathContext (getter: Context)
    getFile (method: File Function(String))
    getFolder (method: Folder Function(String))
    getLink (method: Link Function(String))
    getResource (method: Resource Function(String))
    getStateLocation (method: Folder? Function(String))
    hasOverlay (method: bool Function(String))
    removeOverlay (method: bool Function(String))
    setOverlay (method: void Function(String, {required String content, required int modificationStamp}))
package:analyzer/file_system/physical_file_system.dart:
  PhysicalResourceProvider (class extends Object implements ResourceProvider):
    INSTANCE (static getter: PhysicalResourceProvider)
    new (constructor: PhysicalResourceProvider Function({String? stateLocation}))
    pathContext (getter: Context)
    getFile (method: File Function(String))
    getFolder (method: Folder Function(String))
    getLink (method: Link Function(String))
    getResource (method: Resource Function(String))
    getStateLocation (method: Folder? Function(String))
package:analyzer/instrumentation/file_instrumentation.dart:
  FileInstrumentationLogger (class extends Object implements InstrumentationLogger):
    new (constructor: FileInstrumentationLogger Function(String))
    filePath (getter: String)
    log (method: void Function(String))
    shutdown (method: Future<dynamic> Function())
package:analyzer/instrumentation/instrumentation.dart:
  InstrumentationLogAdapter (class extends Object implements InstrumentationService):
    TAG_ERROR (static getter: String)
    TAG_EXCEPTION (static getter: String)
    TAG_INFO (static getter: String)
    TAG_LOG_ENTRY (static getter: String)
    TAG_NOTIFICATION (static getter: String)
    TAG_PLUGIN_ERROR (static getter: String)
    TAG_PLUGIN_EXCEPTION (static getter: String)
    TAG_PLUGIN_NOTIFICATION (static getter: String)
    TAG_PLUGIN_REQUEST (static getter: String)
    TAG_PLUGIN_RESPONSE (static getter: String)
    TAG_PLUGIN_TIMEOUT (static getter: String)
    TAG_REQUEST (static getter: String)
    TAG_RESPONSE (static getter: String)
    TAG_VERSION (static getter: String)
    TAG_WATCH_EVENT (static getter: String)
    new (constructor: InstrumentationLogAdapter Function(InstrumentationLogger, {Set<String>? watchEventExclusionFiles}))
    logError (method: void Function(String))
    logException (method: void Function(dynamic, [StackTrace?, List<InstrumentationServiceAttachment>?]))
    logInfo (method: void Function(String, [dynamic]))
    logLogEntry (method: void Function(String, DateTime?, String, Object, StackTrace))
    logNotification (method: void Function(String))
    logPluginError (method: void Function(PluginData, String, String, String))
    logPluginException (method: void Function(PluginData, dynamic, StackTrace?))
    logPluginNotification (method: void Function(String, String))
    logPluginRequest (method: void Function(String, String))
    logPluginResponse (method: void Function(String, String))
    logPluginTimeout (method: void Function(PluginData, String))
    logRequest (method: void Function(String))
    logResponse (method: void Function(String))
    logVersion (method: void Function(String, String, String, String, String))
    logWatchEvent (method: void Function(String, String, String))
    shutdown (method: Future<void> Function())
  InstrumentationLogger (class extends Object):
    new (constructor: InstrumentationLogger Function())
    log (method: void Function(String))
    shutdown (method: Future<void> Function())
  InstrumentationService (class extends Object):
    NULL_SERVICE (static getter: NoopInstrumentationService)
    new (constructor: InstrumentationService Function())
    logError (method: void Function(String))
    logException (method: void Function(Object, [StackTrace?, List<InstrumentationServiceAttachment>?]))
    logInfo (method: void Function(String, [dynamic]))
    logLogEntry (method: void Function(String, DateTime, String, Object, StackTrace))
    logNotification (method: void Function(String))
    logPluginError (method: void Function(PluginData, String, String, String))
    logPluginException (method: void Function(PluginData, Object, StackTrace?))
    logPluginNotification (method: void Function(String, String))
    logPluginRequest (method: void Function(String, String))
    logPluginResponse (method: void Function(String, String))
    logPluginTimeout (method: void Function(PluginData, String))
    logRequest (method: void Function(String))
    logResponse (method: void Function(String))
    logVersion (method: void Function(String, String, String, String, String))
    logWatchEvent (method: void Function(String, String, String))
    shutdown (method: Future<void> Function())
  InstrumentationServiceAttachment (class extends Object):
    string (constructor: InstrumentationServiceAttachment Function({required String id, required String value}))
    id (getter: String)
    stringValue (getter: String)
  MulticastInstrumentationService (class extends Object implements InstrumentationService):
    new (constructor: MulticastInstrumentationService Function(List<InstrumentationService>))
    logError (method: void Function(String))
    logException (method: void Function(Object, [StackTrace?, List<InstrumentationServiceAttachment>?]))
    logInfo (method: void Function(String, [dynamic]))
    logLogEntry (method: void Function(String, DateTime, String, Object, StackTrace))
    logNotification (method: void Function(String))
    logPluginError (method: void Function(PluginData, String, String, String))
    logPluginException (method: void Function(PluginData, Object, StackTrace?))
    logPluginNotification (method: void Function(String, String))
    logPluginRequest (method: void Function(String, String))
    logPluginResponse (method: void Function(String, String))
    logPluginTimeout (method: void Function(PluginData, String))
    logRequest (method: void Function(String))
    logResponse (method: void Function(String))
    logVersion (method: void Function(String, String, String, String, String))
    logWatchEvent (method: void Function(String, String, String))
    shutdown (method: Future<void> Function())
  NoopInstrumentationService (class extends Object implements InstrumentationService):
    new (constructor: NoopInstrumentationService Function())
    logError (method: void Function(String))
    logException (method: void Function(Object, [StackTrace?, List<InstrumentationServiceAttachment>?]))
    logInfo (method: void Function(String, [dynamic]))
    logLogEntry (method: void Function(String, DateTime, String, Object, StackTrace))
    logNotification (method: void Function(String))
    logPluginError (method: void Function(PluginData, String, String, String))
    logPluginException (method: void Function(PluginData, Object, StackTrace?))
    logPluginNotification (method: void Function(String, String))
    logPluginRequest (method: void Function(String, String))
    logPluginResponse (method: void Function(String, String))
    logPluginTimeout (method: void Function(PluginData, String))
    logRequest (method: void Function(String))
    logResponse (method: void Function(String))
    logVersion (method: void Function(String, String, String, String, String))
    logWatchEvent (method: void Function(String, String, String))
    shutdown (method: Future<void> Function())
  PluginData (class extends Object):
    new (constructor: PluginData Function(String, String?, String?))
    name (getter: String?)
    pluginId (getter: String)
    version (getter: String?)
    addToFields (method: void Function(List<String>))
package:analyzer/instrumentation/log_adapter.dart:
  InstrumentationLogAdapter (see above)
package:analyzer/instrumentation/logger.dart:
  InstrumentationLogger (see above)
package:analyzer/instrumentation/multicast_service.dart:
  MulticastInstrumentationService (see above)
package:analyzer/instrumentation/noop_service.dart:
  NoopInstrumentationService (see above)
package:analyzer/instrumentation/plugin_data.dart:
  PluginData (see above)
package:analyzer/instrumentation/service.dart:
  InstrumentationService (see above)
  InstrumentationServiceAttachment (see above)
package:analyzer/source/error_processor.dart:
  severityMap (static getter: Map<String, DiagnosticSeverity>)
  ErrorConfig (class extends Object):
    new (constructor: ErrorConfig Function(YamlNode?))
    processors (getter: List<ErrorProcessor>)
  ErrorProcessor (class extends Object):
    getProcessor (static method: ErrorProcessor? Function(AnalysisOptions?, Diagnostic))
    ignore (constructor: ErrorProcessor Function(String))
    new (constructor: ErrorProcessor Function(String, [DiagnosticSeverity?]))
    code (getter: String)
    description (getter: String)
    severity (getter: DiagnosticSeverity?)
    appliesTo (method: bool Function(Diagnostic))
    toString (method: String Function())
package:analyzer/source/file_source.dart:
  FileSource (class extends Source):
    new (constructor: FileSource Function(File, [Uri?]))
    contents (getter: TimestampedData<String>)
    contentsFromFile (getter: TimestampedData<String>)
    file (getter: File)
    fullName (getter: String)
    hashCode (getter: int)
    id (getter: int)
    shortName (getter: String)
    uri (getter: Uri)
    == (method: bool Function(Object))
    exists (method: bool Function())
    toString (method: String Function())
package:analyzer/source/line_info.dart:
  CharacterLocation (class extends Object):
    new (constructor: CharacterLocation Function(int, int))
    columnNumber (getter: int)
    lineNumber (getter: int)
    == (method: bool Function(Object))
    toString (method: String Function())
  LineInfo (class extends Object):
    fromContent (constructor: LineInfo Function(String))
    new (constructor: LineInfo Function(List<int>))
    lineCount (getter: int)
    lineStarts (getter: List<int>)
    getLocation (method: CharacterLocation Function(int))
    getOffsetOfLine (method: int Function(int))
    getOffsetOfLineAfter (method: int Function(int))
package:analyzer/source/source.dart:
  Source (class extends Object):
    new (constructor: Source Function())
    contents (getter: TimestampedData<String>)
    fullName (getter: String)
    hashCode (getter: int)
    shortName (getter: String)
    uri (getter: Uri)
    == (method: bool Function(Object))
    exists (method: bool Function())
package:analyzer/source/source_range.dart:
  SourceRange (class extends Object):
    EMPTY (static getter: SourceRange)
    new (constructor: SourceRange Function(int, int))
    end (getter: int)
    hashCode (getter: int)
    length (getter: int)
    offset (getter: int)
    == (method: bool Function(Object))
    contains (method: bool Function(int))
    containsExclusive (method: bool Function(int))
    coveredBy (method: bool Function(SourceRange))
    covers (method: bool Function(SourceRange))
    endsIn (method: bool Function(SourceRange))
    getExpanded (method: SourceRange Function(int))
    getMoveEnd (method: SourceRange Function(int))
    getTranslated (method: SourceRange Function(int))
    getUnion (method: SourceRange Function(SourceRange))
    intersects (method: bool Function(SourceRange?))
    startsIn (method: bool Function(SourceRange))
    toString (method: String Function())
package:analyzer/src/dart/analysis/experiments_impl.dart:
  ExperimentalFeature (class extends Object implements Feature):
    new (constructor: ExperimentalFeature Function({List<String> channels, required String documentation, required String enableString, required Version? experimentalReleaseVersion, required int index, required bool isEnabledByDefault, required bool isExpired, required Version? releaseVersion}))
    channels (getter: List<String>)
    disableString (getter: String)
    documentation (getter: String)
    enableString (getter: String)
    experimentalFlag (getter: String?)
    experimentalReleaseVersion (getter: Version?)
    index (getter: int)
    isEnabledByDefault (getter: bool)
    isExpired (getter: bool)
    releaseVersion (getter: Version?)
    status (getter: FeatureStatus)
    stringForValue (method: String Function(bool))
    toString (method: String Function())
package:analyzer/src/dart/ast/ast.dart:
  AttemptedConstantEvaluationResult (class extends Object):
    diagnostics (getter: List<Diagnostic>)
    value (getter: DartObject?)
package:analyzer/src/dart/resolver/scope.dart:
  Namespace (class extends Object):
    EMPTY (static getter: Namespace)
    EMPTY= (static setter: Namespace)
    new (constructor: Namespace Function(Map<String, Element>))
    definedNames2 (getter: Map<String, Element>)
    get2 (method: Element? Function(String))
    getPrefixed2 (method: Element? Function(String, String))
package:analyzer/src/generated/timestamped_data.dart:
  TimestampedData (class<E> extends Object):
    new (constructor: TimestampedData<E> Function(int, E))
    data (getter: E)
    modificationTime (getter: int)
package:analyzer/src/lint/config.dart:
  RuleConfig (non-public)
package:analyzer/src/workspace/workspace.dart:
  Workspace (non-public)
package:analyzer/utilities/extensions/ast.dart:
  AstNodeExtension (extension on AstNode, deprecated):
    nodeCovering (method: AstNode? Function({int length, required int offset}), deprecated)
package:analyzer/utilities/extensions/element.dart:
  LibraryElement2Extension (extension on LibraryElement):
    exportedExtensions (getter: Iterable<ExtensionElement>)
package:analyzer/utilities/extensions/uri.dart:
  UriExtension (extension on Uri):
    isImplementation (getter: bool)
    isSamePackageAs (method: bool Function(Uri))
package:analyzer/utilities/package_config_file_builder.dart:
  PackageConfigFileBuilder (class extends Object):
    new (constructor: PackageConfigFileBuilder Function())
    add (method: void Function({String? languageVersion, required String name, String packageUri, required String rootPath}))
    copy (method: PackageConfigFileBuilder Function())
    toContent (method: String Function({required Context pathContext}))
package:analyzer/workspace/workspace.dart:
  WorkspacePackage (class extends Object):
    new (constructor: WorkspacePackage Function())
    canHavePublicApi (getter: bool)
    root (getter: Folder)
    contains (method: bool Function(Source))
    isInTestDirectory (method: bool Function(File))
dart:async:
  Future (referenced)
  Stream (referenced)
dart:core:
  Comparable (referenced)
  DateTime (referenced)
  Duration (referenced)
  Exception (referenced)
  Iterable (referenced)
  Iterator (referenced)
  List (referenced)
  Map (referenced)
  Null (referenced)
  Object (referenced)
  Set (referenced)
  StackTrace (referenced)
  Stopwatch (referenced)
  String (referenced)
  StringBuffer (referenced)
  StringSink (referenced)
  Uri (referenced)
  bool (referenced)
  double (referenced)
  int (referenced)
dart:typed_data:
  Uint8List (referenced)
package:_fe_analyzer_shared/src/scanner/token.dart:
  KeywordStyle (enum):
    builtIn (static getter: KeywordStyle)
    pseudo (static getter: KeywordStyle)
    reserved (static getter: KeywordStyle)
    values (static getter: List<KeywordStyle>)
  SimpleToken (class extends Object implements Token):
    new (constructor: SimpleToken Function(TokenType, int, [CommentToken?]))
    beforeSynthetic (getter: Token?)
    beforeSynthetic= (setter: Token?)
    charCount (getter: int)
    charEnd (getter: int)
    charOffset (getter: int)
    end (getter: int)
    endGroup (getter: Token?)
    isEof (getter: bool)
    isIdentifier (getter: bool)
    isKeyword (getter: bool)
    isKeywordOrIdentifier (getter: bool)
    isModifier (getter: bool)
    isOperator (getter: bool)
    isSynthetic (getter: bool)
    isTopLevelKeyword (getter: bool)
    isUserDefinableOperator (getter: bool)
    keyword (getter: Keyword?)
    kind (getter: int)
    length (getter: int)
    lexeme (getter: String)
    next (getter: Token?)
    next= (setter: Token?)
    offset (getter: int)
    offset= (setter: int)
    precedingComments (getter: CommentToken?)
    precedingComments= (setter: CommentToken?)
    previous (getter: Token?)
    previous= (setter: Token?)
    stringValue (getter: String?)
    type (getter: TokenType)
    typeIndex (getter: int)
    matchesAny (method: bool Function(List<TokenType>))
    setNext (method: Token Function(Token))
    setNextWithoutSettingPrevious (method: Token? Function(Token?))
    toString (method: String Function())
    value (method: Object Function())
  StringToken (class extends SimpleToken):
    new (constructor: StringToken Function(TokenType, String, int, [CommentToken?]))
    isIdentifier (getter: bool)
    lexeme (getter: String)
    value (method: String Function())
package:path/src/context.dart:
  Context (referenced)
package:pub_semver/src/version.dart:
  Version (referenced)
package:source_span/src/span.dart:
  SourceSpan (referenced)
package:watcher/src/watch_event.dart:
  WatchEvent (referenced)
package:yaml/src/yaml_node.dart:
  YamlNode (referenced)
