enum TestEnum {
  value1, value2, value3;
  
  void printInfo() {
    print('Enum value: $name, index: $index');
  }
}

class TestClass {
  int? field1;
  String? field2;
  
  TestClass(this.field1, this.field2);
  
  void method1() {
    print('Method1: field1=$field1, field2=$field2');
  }
  
  void method2() {
    print('Method2 called');
  }
}

void main() {
  print('Testing incremental bytecode loading...');
  
  // 测试枚举
  for (TestEnum value in TestEnum.values) {
    value.printInfo();
  }
  
  // 测试类
  var testObj = TestClass(42, 'Hello');
  testObj.method1();
  testObj.method2();
  
  print('Test completed successfully!');
}
