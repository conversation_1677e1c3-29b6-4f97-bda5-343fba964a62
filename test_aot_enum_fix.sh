#!/bin/bash

# Test script for AOT enum bytecode loading fix
# This script tests the fix for the _Enum field lookup issue in AOT mode

echo "Testing AOT enum bytecode loading fix..."

# Set up test environment
DART_SDK_ROOT="/Volumes/yami/Tencent/flutter/engine/src/flutter/third_party/dart"
TEST_FILE="test_enum_bytecode_loading.dart"

# Check if dart executable exists
if [ ! -f "$DART_SDK_ROOT/out/ReleaseX64/dart" ]; then
    echo "Error: Dart executable not found. Please build the Dart SDK first."
    echo "Run: cd $DART_SDK_ROOT && make -j8"
    exit 1
fi

# Test 1: Compile to AOT
echo "Step 1: Compiling test to AOT..."
$DART_SDK_ROOT/out/ReleaseX64/dart compile aot-snapshot $TEST_FILE -o test_enum.aot

if [ $? -eq 0 ]; then
    echo "✓ AOT compilation successful"
else
    echo "✗ AOT compilation failed"
    exit 1
fi

# Test 2: Run AOT snapshot
echo "Step 2: Running AOT snapshot..."
$DART_SDK_ROOT/out/ReleaseX64/dartaotruntime test_enum.aot

if [ $? -eq 0 ]; then
    echo "✓ AOT execution successful"
else
    echo "✗ AOT execution failed"
    exit 1
fi

echo "All tests passed! The fix appears to be working."

# Cleanup
rm -f test_enum.aot

echo "Test completed."
