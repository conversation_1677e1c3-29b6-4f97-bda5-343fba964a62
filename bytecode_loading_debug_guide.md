# Dart Bytecode 加载调试日志指南

## 概述

我已经在 `runtime/vm/bytecode_reader.cc` 的关键位置添加了详细的 `std::cout` 日志输出，帮助你深入了解bytecode加载的整个过程。这些日志将显示从文件读取到对象创建的每个步骤。

## 添加的日志点

### 1. 主加载流程 (BytecodeLoader::LoadBytecode)

**位置**: 第84-116行
**输出内容**:
```
=== BYTECODE LOADING STARTED ===
Binary size: [文件大小] bytes
Step 1: Reading bytecode component...
Step 1: Bytecode component read successfully
Step 2: Reading library declarations...
Number of libraries: [库数量]
Library index offset: [偏移量]
Step 2: Library declarations read successfully
Step 3: Reading main function...
Main function offset: [偏移量]
Step 3: Main function read successfully
=== BYTECODE LOADING COMPLETED ===
```

### 2. 文件头解析 (ReadBytecodeComponent)

**位置**: 第754-780行
**输出内容**:
```
--- ReadBytecodeComponent: Starting file header parsing ---
File start offset: 0
Magic number: 0x44424333 (Valid DBC3)
Format version: 1 (Supported)
```

### 3. Section偏移读取

**位置**: 第783-810行
**输出内容**:
```
--- Reading section offsets ---
String table: [数量] items at offset [偏移]
Object table: [数量] items at offset [偏移]
Main section: [数量] items at offset [偏移]
Library index: [数量] libraries at offset [偏移]
Libraries: [数量] items at offset [偏移]
Classes: [数量] classes at offset [偏移]
Members: [数量] items at offset [偏移]
Codes: [数量] codes at offset [偏移]
Source positions: [数量] items at offset [偏移]
```

### 4. 字符串表和对象表详情

**位置**: 第831-850行
**输出内容**:
```
--- Reading string table header ---
String table details:
  - One-byte strings: [数量]
  - Two-byte strings: [数量]
  - String contents offset: [偏移]

--- Reading object table header ---
Object table details:
  - Number of objects: [数量]
  - Objects content size: [大小] bytes
  - Objects contents offset: [偏移]
  - Object offsets table offset: [偏移]
```

### 5. 延迟加载设置

**位置**: 第870-885行
**输出内容**:
```
--- Reading object offsets (lazy loading setup) ---
  Object[0] -> offset [偏移] (stored as Smi)
  Object[1] -> offset [偏移] (stored as Smi)
  ...
  Object[N] -> offset [偏移] (stored as Smi)
Total objects prepared for lazy loading: [数量]
--- ReadBytecodeComponent completed ---
```

### 6. 递归对象读取 (ReadObject)

**位置**: 第904-950行
**输出内容**:
```
  ReadObject[depth=1]: header=0x[值] -> Reference to object[索引]
    Loading from offset: [绝对偏移] (base+[相对偏移])
    Object header: 0x[值]
    Object[索引] loaded and cached successfully
```

或者对于缓存命中:
```
  ReadObject[depth=1]: header=0x[值] -> Reference to object[索引] (cache HIT - already loaded)
```

### 7. 对象内容解析 (ReadObjectContents)

**位置**: 第1008-1025行
**输出内容**:
```
  ReadObjectContents[depth=1]: Library (kind=1, flags=0x0)
    Reading library URI...
    Looking up library: '[URI]'
    Library found successfully
```

```
  ReadObjectContents[depth=1]: Class (kind=2, flags=0x0)
    Reading class library...
    Reading class name...
    Looking up class: '[类名]' in library: '[库名]'
    Class found successfully
```

```
  ReadObjectContents[depth=1]: Member (kind=3, flags=0x[值])
    Reading member class...
    Reading member name...
    Looking up function: '[函数名]' in class: '[类名]'
    Function found successfully
```

### 8. 函数代码加载 (ReadCode)

**位置**: 第161-175行和228-240行
**输出内容**:
```
*** ReadCode: Loading bytecode for function: [完整函数名] ***
Function kind: [函数类型]
Code offset: [偏移]
Reading constant pool...
Constant pool size: [数量] entries
Constant pool loaded successfully
Reading bytecode instructions...
Bytecode attached to function successfully
*** ReadCode completed for function: [完整函数名] ***
```

## 如何使用这些日志

### 1. 编译VM
```bash
# 确保包含调试信息
./tools/build.py --mode debug --arch x64
```

### 2. 运行带日志的程序
```bash
# 运行你的Dart程序，日志会自动输出到控制台
out/DebugX64/dart your_program.dart
```

### 3. 日志输出示例

运行一个简单的"Hello World"程序时，你会看到类似这样的输出：

```
=== BYTECODE LOADING STARTED ===
Binary size: 2048 bytes

--- ReadBytecodeComponent: Starting file header parsing ---
File start offset: 0
Magic number: 0x44424333 (Valid DBC3)
Format version: 1 (Supported)

--- Reading section offsets ---
String table: 5 items at offset 100
Object table: 8 items at offset 228
Main section: 1 items at offset 324
Library index: 1 libraries at offset 300
...

--- Reading object offsets (lazy loading setup) ---
  Object[0] -> offset 0 (stored as Smi)
  Object[1] -> offset 260 (stored as Smi)
  Object[2] -> offset 280 (stored as Smi)
  ...

Step 1: Bytecode component read successfully
Step 2: Reading library declarations...
  ReadObject[depth=1]: header=0x3 -> Reference to object[1] (cache MISS - loading now)
    Loading from offset: 260 (base+0)
    Object header: 0x2
    ReadObjectContents[depth=1]: Library (kind=1, flags=0x0)
      Reading library URI...
        ReadObject[depth=2]: header=0x15 -> Reference to object[5]
        ...
      Looking up library: 'file:///hello.dart'
      Library found successfully
    Object[1] loaded and cached successfully
...

Step 3: Reading main function...
*** ReadCode: Loading bytecode for function: hello.dart::main ***
Function kind: RegularFunction
Code offset: 400
Reading constant pool...
Constant pool size: 3 entries
Constant pool loaded successfully
Reading bytecode instructions...
Bytecode attached to function successfully
*** ReadCode completed for function: hello.dart::main ***

=== BYTECODE LOADING COMPLETED ===
```

## 调试技巧

### 1. 过滤特定信息
```bash
# 只看主要步骤
dart your_program.dart 2>&1 | grep "Step\|==="

# 只看对象加载
dart your_program.dart 2>&1 | grep "ReadObject\|cache"

# 只看函数加载
dart your_program.dart 2>&1 | grep "ReadCode\|Function"
```

### 2. 保存日志到文件
```bash
dart your_program.dart > bytecode_loading.log 2>&1
```

### 3. 分析递归深度
通过观察 `[depth=N]` 标记，你可以了解对象依赖的复杂程度。

### 4. 监控缓存效率
通过统计 "cache HIT" vs "cache MISS" 的比例，可以了解延迟加载的效果。

## 注意事项

1. **性能影响**: 这些日志会显著影响加载性能，仅用于调试目的
2. **输出量大**: 复杂程序会产生大量日志，建议使用过滤或重定向
3. **递归显示**: 深度递归的对象会产生缩进的嵌套输出
4. **线程安全**: 在多线程环境下，日志可能会交错显示

## 移除日志

如果需要移除这些调试日志，可以：
1. 删除所有包含 `std::cout` 的行
2. 删除 `#include <iostream>` 
3. 删除静态变量 `read_depth` 和 `content_depth`
4. 重新编译VM

这些日志将帮助你深入理解Dart VM如何加载和解析bytecode文件，从文件格式验证到对象创建的每个步骤都清晰可见。
