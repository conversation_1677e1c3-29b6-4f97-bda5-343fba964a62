// Test case for AOT incremental bytecode loading with enum fields
// This test reproduces the issue where _Enum fields are missing in AOT

enum TestEnum {
  value1,
  value2,
  value3;
  
  void printInfo() {
    print('Enum value: $name, index: $index');
  }
}

void main() {
  print('Testing enum bytecode loading...');
  
  for (TestEnum value in TestEnum.values) {
    value.printInfo();
  }
  
  print('Test completed successfully!');
}
